/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzColorPicker from '@/components/LzColorPicker';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="活动商品装修">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="商品列表背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={740}
                  height={1345}
                  value={formData.skuListBg}
                  onChange={(skuListBg) => {
                    setForm({ skuListBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：741*1477px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ skuListBg: defaultValue?.skuListBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="门槛按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={150}
                  height={58}
                  value={formData.stepBtnBg}
                  onChange={(stepBtnBg) => {
                    setForm({ stepBtnBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：150*58px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ stepBtnBg: defaultValue?.stepBtnBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="门槛按钮选中">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={150}
                  height={58}
                  value={formData.stepBtnSelectBg}
                  onChange={(stepBtnSelectBg) => {
                    setForm({ stepBtnSelectBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：150*58px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ stepBtnSelectBg: defaultValue?.stepBtnSelectBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="商品背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={300}
                  height={350}
                  value={formData.skuBg}
                  onChange={(skuBg) => {
                    setForm({ skuBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：300px*350px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ skuBg: defaultValue?.skuBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="商品标题背景图"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={210} */}
          {/*        height={51} */}
          {/*        value={formData.skuTitleBg} */}
          {/*        onChange={(skuTitleBg) => { */}
          {/*          setForm({ skuTitleBg }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：210px*51px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ skuTitleBg: defaultValue?.skuTitleBg }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
          <Form.Item label="商品标题颜色">
            <LzColorPicker value={formData.skuTitleColor} onChange={(skuTitleColor) => setForm({ skuTitleColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ skuTitleColor: defaultValue?.skuTitleColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="立即购买按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={244}
                  height={81}
                  value={formData.goSkuBtn}
                  onChange={(goSkuBtn) => {
                    setForm({ goSkuBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：244px*81px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ goSkuBtn: defaultValue?.goSkuBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
