/**
 * Author: zhangyue
 * Date: 2023-05-29 18:02
 * Description: 大转盘
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzColorPicker from '@/components/LzColorPicker';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };

  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel title="奖品展示">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="奖品区域背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  height={343}
                  value={formData.prizeContentBg}
                  onChange={(prizeContentBg) => {
                    setForm({ prizeContentBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：750*343px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ prizeContentBg: defaultValue?.prizeContentBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="奖品背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={239}
                  height={305}
                  value={formData.prizeBg}
                  onChange={(prizeBg) => {
                    setForm({ prizeBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：239*305px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ prizeBg: defaultValue?.prizeBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="奖品名称文字颜色">
            <LzColorPicker value={formData.prizeNameColor} onChange={(prizeNameColor) => setForm({ prizeNameColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ prizeNameColor: defaultValue?.prizeNameColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzPanel title="抽奖">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="抽奖次数文案颜色">
            <LzColorPicker value={formData.drawsNum} onChange={(drawsNum) => setForm({ drawsNum })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ drawsNum: defaultValue?.drawsNum });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="抽奖按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={282}
                  height={70}
                  value={formData.drawBtn}
                  onChange={(drawBtn) => {
                    setForm({ drawBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：282*70px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ drawBtn: defaultValue?.drawBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
