/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue } from '../../../util';
import LzPanel from '@/components/LzPanel';

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="进店逛逛设置">
        <Form.Item label="背景图">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={750}
                height={96}
                value={formData.goToShopBg}
                onChange={(goToShopBg) => {
                  setForm({ goToShopBg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>图片尺寸：宽度750px*96px</p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG、GIF</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setForm({ goToShopBg: defaultValue?.goToShopBg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
      </LzPanel>
    </div>
  );
};
