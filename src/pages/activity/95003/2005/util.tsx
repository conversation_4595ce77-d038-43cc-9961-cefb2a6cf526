import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  shopNameColor: string;
  btnBgColor: string;
  btnColor: string;
  btnBg: string;
  btnBorderColor: string;
  wheelBg: string;
  wheelPanel: string;
  wheelBtn: string;
  drawBtn: string;
  wheelTextColor: string;
  drawsNum: string;
  winnersBg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  drawsNumBg: string;
}

export interface PageData {
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  partake: number;
  partakeRangeData: [string, string] | null;
  partakeStartTime: string;
  partakeEndTime: string;
  crowdPackage: number;
  limitOrder: number;
  orderRestrainRangeData: [Dayjs, Dayjs];
  orderStartTime: string;
  orderEndTime: string;
  orderStatus: number;
  priceCalculateType: number;
  orderPriceType: number;
  split: number;
  orderRestrainAmount: string;
  orderSkuisExposure: number;
  orderSkuList: any[];
  crowdBag: any;
  totalProbability: number;
  prizeList: PrizeInfo[]; // 根据实际情况，可能需要定义奖品的类型
  // taskList: any[]; // 根据实际情况，可能需要定义任务的类型
  drawLimit: number;
  drawNum: number;
  winLotteryLimit: number;
  winLotteryNum: number;
  isExposure: number;
  skuList: any[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  drawTimesType: number;
  drawTimesList: any[];
  luckyDrawTimeRangeData: [Dayjs, Dayjs];
  drawStartTime: string;
  drawEndTime: string;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBgColor: '',
  btnBg: '',
  btnBorderColor: '',
  // 奖盘背景图
  wheelBg: '',
  wheelPanel: '',
  wheelBtn: '',
  // 奖盘按钮背景图
  drawBtn: '',
  // 奖盘文案颜色
  wheelTextColor: '',
  // 抽奖次数文案颜色
  drawsNum: '',
  // 获奖名单背景图
  winnersBg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  drawsNumBg: '',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `${dayjs().month() + 1}月消费满额抽奖(实付金额交易完成)`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 参与时段限制（不提交）
    partake: 0,
    // 参与时段限制（不提交）
    partakeRangeData: null,
    // 参与开始时间
    partakeStartTime: '',
    // 参与结束时间
    partakeEndTime: '',
    // 参与者生成人群包
    crowdPackage: 0,
    // 限制订单
    limitOrder: 1,
    // 限制订单时间
    orderRestrainRangeData: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 限制订单开始时间
    orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 限制订单结束时间
    orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    orderStatus: 1, // 订单结算状态 交易完成
    priceCalculateType: 1, // 订单金额计算方式(1-累计金额)
    orderPriceType: 1, // 订单价格结算方式(1-实付金额)
    // 是否拆单0否1是
    split: 0,
    // 限制订单金额
    orderRestrainAmount: '',
    // 订单商品全点商品或指定商品
    orderSkuisExposure: 0,
    // 限制订单商品列表
    orderSkuList: [],

    // 中奖总概率
    totalProbability: 0,
    // 奖品列表
    prizeList: [],
    // 是否限制每人每天中奖次数 1 不限制 2 限制
    drawLimit: 1,
    // 每人每天中奖次数
    drawNum: 1,
    // 是否限制每人累计次数 1 不限制 2 限制
    winLotteryLimit: 1,
    // 每人累计中奖次数
    winLotteryNum: 1,
    // 是否开启曝光
    isExposure: 1,
    // 商品列表
    skuList: [],
    // 分享标题
    shareStatus: 1, // 1开启 0关闭
    shareTitle: '下单即可抽好礼',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    drawTimesType: 1, // 1 固定金额倍数 2 自定义阶梯
    drawTimesList: [
      {
        orderPrice: 100,
        drawNum: 1,
        sort: 1,
      },
    ],
    // 抽奖时间
    luckyDrawTimeRangeData: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 抽奖开始时间
    drawStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 抽奖结束时间
    drawEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '谢谢参与',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};
// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

// 校验订单时间是否符合规则
const isOrderTimeValid = (formData: PageData): boolean => {
  const orderStart = dayjs(formData.orderRestrainRangeData[0]);
  const orderEnd = dayjs(formData.orderRestrainRangeData[1]);
  const isEnd: boolean = dayjs(formData.orderEndTime).isAfter(dayjs(formData.endTime));
  if (isEnd) {
    Message.error('订单结算周期应小于活动结束时间');
    return false;
  }
  if (!orderEnd.isAfter(orderStart) || orderEnd.isSame(orderStart)) {
    Message.error('订单结算周期结束时间应大于订单结算周期开始时间');
    return false;
  }
  return true;
};
// 校验抽奖时间是否符合规则
const isDrawTimeValid = (formData: PageData): boolean => {
  const drawStartTime = dayjs(formData.luckyDrawTimeRangeData[0]);
  const drawEndTime = dayjs(formData.luckyDrawTimeRangeData[1]);
  const isEnd: boolean = dayjs(formData.drawEndTime).isAfter(dayjs(formData.endTime));
  if (isEnd) {
    Message.error('抽奖结束时间应小于活动结束时间');
    return false;
  }
  if (!drawEndTime.isAfter(drawStartTime) || drawEndTime.isSame(drawStartTime)) {
    Message.error('抽奖结束时间应大于抽奖开始时间');
    return false;
  }
  return true;
};
const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  if (!prizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  return true;
};

// const winCounts = (formData: PageData): boolean => {
//   if (formData.winLotteryLimit === 2 && formData.drawLimit === 2 && formData.drawNum > formData.winLotteryNum) {
//     Message.error('每日中奖次数不能大于累计中奖次数');
//     return false;
//   }
//   return true;
// };
const checkStepAmount = (formData: PageData): boolean => {
  if (formData.drawTimesList.length === 0) {
    Message.error('请设置新增抽奖次数！');
    return false;
  }
  const maxChance = Math.max(...formData.drawTimesList.map((item: any) => item.drawNum));
  if (formData.drawLimit == 2 && formData.drawNum < maxChance) {
    Message.error('抽奖次数不能大于最大抽奖次数！');
    return false;
  }
  for (let i = 0; i < formData.drawTimesList.length; i++) {
    if (!formData.drawTimesList[i].orderPrice) {
      Message.error(`请设置阶梯 ${i + 1} 级的累计金额！`);
      return false;
    }
    if (!formData.drawTimesList[i].drawNum) {
      Message.error(`请设置阶梯 ${i + 1} 级的抽奖次数！`);
      return false;
    }
    if (
      formData.drawTimesList[i + 1] &&
      formData.drawTimesList[i + 1].orderPrice <= formData.drawTimesList[i].orderPrice
    ) {
      Message.error(`阶梯 ${i + 2} 级的累计金额应大于阶梯 ${i + 1} 级的累计金额！`);
      return false;
    }
    if (formData.drawTimesList[i + 1] && formData.drawTimesList[i + 1].drawNum <= formData.drawTimesList[i].drawNum) {
      Message.error(`阶梯 ${i + 2} 级的抽奖次数应大于阶梯 ${i + 1} 级的抽奖次数！`);
      return false;
    }
  }
  return true;
};
export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType() && +getParams('status') === 2) {
    return true;
  }
  if (!checkStepAmount(formData)) {
    return false;
  }
  // 没有选择奖品
  if (!hasPrize(prizeList)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 订单校验时间异常
  if (!isOrderTimeValid(formData)) {
    return false;
  }
  // 抽奖校验时间异常
  if (!isDrawTimeValid(formData)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(prizeList, formData)) {
    return false;
  }
  // 中奖次数限制
  // if (!winCounts(formData)) {
  //   return false;
  // }
  return true;
};
