import React, { useEffect, useState } from 'react';
import { Form, DatePicker2, Field, Table, Button, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataDetailQuery, dataDetailExport } from '@/api/v99101';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.startDate = dayJs(query.dateRange[0]).format('YYYY-MM-DD HH:mm:ss');
    query.endDate = dayJs(query.dateRange[1]).format('YYYY-MM-DD HH:mm:ss');
    dataDetailQuery(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    formValue.startDate = dayJs(formValue.dateRange[0]).format('YYYY-MM-DD HH:mm:ss');
    formValue.endDate = dayJs(formValue.dateRange[1]).format('YYYY-MM-DD HH:mm:ss');
    dataDetailExport(formValue).then((data: any) => downloadExcel(data, '领取记录'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        {/* <Form.Item name="pin" label="用户pin"> */}
        {/*  <Input placeholder="请输入用户pin" /> */}
        {/* </Form.Item> */}
        <FormItem name="dateRange" label="领取时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        {/* <Form.Item name="status" label="领取状态"> */}
        {/*  <Select followTrigger mode="single" defaultValue="" style={{ marginRight: 8 }} dataSource={STATUS} /> */}
        {/* </Form.Item> */}
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        {/* <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}> */}
        {/*  生成人群包 */}
        {/* </Button> */}
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column
          title="用户昵称"
          lock={'left'}
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nick ? Utils.mask(row.nick) : '-'}
                  {row.nick && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nick).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.pin ? Utils.mask(row.pin) : '-'}
                  {row.pin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.pin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="领取时间"
          dataIndex="createTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.sendTime)}</div>}
        />
        <Table.Column title="系列名称" dataIndex="seriesName" />
        <Table.Column title="阶梯名称" dataIndex="stepName" />
        <Table.Column title="奖品类型" dataIndex="prizeType" />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column title="领取状态" dataIndex="sendState" />
        <Table.Column
          title="取消/发放时间"
          dataIndex="createTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.updateTime)}</div>}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      {/* <LzDialog */}
      {/*  title="生成人群包" */}
      {/*  className="lz-dialog-mini" */}
      {/*  visible={packVisible} */}
      {/*  footer={false} */}
      {/*  onCancel={() => setPackVisible(false)} */}
      {/*  onClose={() => setPackVisible(false)} */}
      {/* > */}
      {/*  <LzGenerateCrowdBag */}
      {/*    dataUploadPin={lotteryUploadPin} */}
      {/*    formValue={field.getValues()} */}
      {/*    cancel={() => setPackVisible(false)} */}
      {/*  /> */}
      {/* </LzDialog> */}
    </div>
  );
};
