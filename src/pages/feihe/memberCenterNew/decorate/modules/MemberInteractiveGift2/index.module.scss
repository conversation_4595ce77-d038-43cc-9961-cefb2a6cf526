.preview {
  overflow: hidden;
  background: #efefef;
  border-radius: 8.5px;

  .empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 126px;
    color: #000;
    border-bottom: 1px solid #fff;
  }

  .previewItem {
    position: relative;

    img {
      width: 100%;
      vertical-align: middle;
      -webkit-user-drag: none;
    }

    .hotZone {
      position: absolute;
      color: #fff;
      background: rgba(0, 0, 0, 50%);
    }
  }
}

.addBtn {
  width: 100%;
  margin-top: -5px;
  margin-bottom: 10px;
}

.operation {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 15px;

  .hotContainer {
    position: relative;
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;

    .imgUpload {
      display: flex;

      .removeHotZone {
        margin-top: 4px;

        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }

    .setting {
      margin-left: 15px;

      .btn {
        width: 300px;
      }

      .urlContainer {
        position: relative;
        display: flex;
        align-items: center;
        width: 300px;
        height: 28px;
        margin-top: 8px;
        padding: 0 10px;
        background: #fff;
        border-radius: 5px;

        .url {
          display: flex;
          width: 100%;
          overflow-x: scroll;
          white-space: nowrap;
        }

        .url::-webkit-scrollbar {
          display: none;
        }

        i {
          position: absolute;
          top: 5px;
          right: -17px;
          font-size: 12px;
          cursor: pointer;
        }
      }
    }
  }
}
