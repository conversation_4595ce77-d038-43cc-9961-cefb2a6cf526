/**
 * Author: lin
 * Date: 2024-12-05 15:37
 * Description:
 */
import React, { useReducer, useState, useEffect } from 'react';
// @ts-ignore
import styles from '../../index.module.scss';
import { Field, Form, NumberPicker, Button, Divider, Message, Balloon, Icon, Select, Radio } from '@alifd/next';
import ChooseGoods from '../../../Dialog/ChooseGoods';
import format from '@/utils/format';
// import dayjs from 'dayjs';

const FormItem = Form.Item;

// const dateFormat = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: any = {
  labelAlign: 'top',
  colon: true,
};

const TIP = (
  <div style={{ color: 'black' }}>
    <div>任务说明</div>
    <div>1.用户浏览指定商品可以获得补签机会</div>
    <div>2.活动内商品只能浏览一次，全部浏览完成则达任务上限</div>
    <div>3.达到每日内完成任务上限后，用户再浏览商品无法获得补签机会</div>
  </div>
);
export default ({ onCancel, onSubmit, editValue, formData }) => {
  const defaultValue = {
    // 浏览限制 每日||活动期内
    limitType: 1,
    // 每日||活动期内可完成次数
    totalLimit: 1,
    // 每次任务需要完成的子任务数 (默认为1)
    needTimesPerTask: 1,
    // 每次完成任务给予的奖励数
    giveChancePerTask: 1,
    // 全店sku 2 || 指定sku 1
    skuLimitType: 2,
    // 商品列表
    skuList: [],
  };

  const [taskData, setTaskData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);

  const [totalTimes, setTotalTimes] = useState(0);
  const field = Field.useField();

  useEffect(() => {
    totalChanceLimit();
  }, [taskData.limitType, taskData.totalLimit, taskData.giveChancePerTask]);

  // 可获得补签卡总数
  const totalChanceLimit = () => {
    if (taskData.limitType === 1) {
      setTotalTimes(taskData.totalLimit * taskData.giveChancePerTask * formData.duration);
    } else {
      setTotalTimes(taskData.totalLimit * taskData.giveChancePerTask);
    }
  };

  const setData = (data) => {
    setTaskData(data);
  };

  const submit = () => {
    if (taskData.skuLimitType === 1 && !taskData.skuList.length) {
      Message.error('请添加商品');
      return;
    }
    onSubmit({ ...taskData, taskType: 3, dayLimit: taskData.totalLimit, totalChanceLimit: totalTimes });
  };

  const handleSkuChange = (data) => {
    setData({ skuList: data });
  };

  const TipLabel = (
    <span>
      <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
        1）选择每天：
        <br />
        【此任务最多可得补签卡数】=【活动时间段自然日】*【每天可完成次数】*【每完成一次可得补签卡数】
        <br />
        2）选择活动期间内：
        <br />
        【此任务最多可得补签卡数】=【活动期间可完成次数】【每完成一次可得补签卡数】
      </Balloon.Tooltip>
    </span>
  );

  return (
    <div className={styles.lzTask}>
      <div className={styles.divRow}>任务条件: 完成商品浏览</div>
      <Form {...formItemLayout} field={field}>
        <FormItem label="可参与次数" className={styles.panel}>
          <div>
            <Select
              className={styles.select}
              dataSource={[
                { value: 1, label: '每日' },
                { value: 2, label: '活动期间内' },
              ]}
              value={taskData.limitType}
              onChange={(limitType) => setData({ limitType })}
            />
            支持完成
            <NumberPicker
              value={taskData.totalLimit}
              onChange={(totalLimit) => setData({ totalLimit })}
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
            />
            次
          </div>
          <p className={styles.tip}> 注：每浏览一件商品算作完成一次任务</p>
          <div>
            每完成一次浏览任务可获得
            <NumberPicker
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
              value={taskData.giveChancePerTask}
              onChange={(giveChancePerTask) => setData({ giveChancePerTask })}
            />
            张补签卡
          </div>
        </FormItem>
        <FormItem colon={false} className={styles.panel}>
          <span className={styles.tip}> 此任务最多可获得</span>
          <span className={styles.numberSpan}>{totalTimes}</span>
          张补签卡
          <span>{TipLabel}</span>
        </FormItem>

        <FormItem label="可浏览商品" colon={false}>
          <Radio.Group
            className={styles.radio}
            defaultValue={taskData.skuLimitType}
            onChange={(skuLimitType) => setData({ skuLimitType })}
          >
            <Radio id="2" value={2}>
              全店商品
            </Radio>
            <Radio id="1" value={1}>
              指定商品
            </Radio>
          </Radio.Group>

          {taskData.skuLimitType === 1 && (
            <div className={styles.panel}>
              {/* <FormItem label="商品信息" required requiredMessage="请添加商品"> */}
              <ChooseGoods max={500} value={taskData.skuList} onChange={handleSkuChange} />
              <p className={styles.tip}>
                注：最少添加{taskData.needTimesPerTask * taskData.dailyLimit || '0'}个，最多添加500个SKU
              </p>
              {/* </FormItem> */}
            </div>
          )}
        </FormItem>
      </Form>
      <Divider />
      <div className={styles.footer}>
        <Button onClick={onCancel}>取消</Button>
        <Button onClick={submit} type="primary">
          确定
        </Button>
      </div>
    </div>
  );
};
