import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  signDay: string;
  orderPrice?: number;
  sendPeople?: number;
}

export interface CustomValue {
  actBg: string; // 主页背景色
  actBgColor: string;
  shopNameColor: string; // 店铺名称颜色
  btnColor: string; // 按钮字体颜色
  btnBg: string; // 按钮背景颜色
  btnBorderColor: string; // 按钮边框颜色
  cutDownBg: string; // 倒计时背景
  cutDownColor: string; // 倒计时字体颜色
  cutDownNumBg: string; // 倒计时数字背景
  prizeBg: string; // 奖品背景
  prizeNameColor: string; // 奖品名称颜色
  getPrizeBtn: string; // 领取按钮
  cmdImg: string; // 命令图片
  h5Img: string; // h5图片
  mpImg: string; // 小程序图片
  exchangeBtn: string; // 兑换按钮
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  // crowdPackage: number;
  awardStartTime: string;
  awardEndTime: string;
  awardType: number;
  prizeList: PrizeInfo[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;
  isDelayedDisttribution: number;
  awardDays: number;
  // limitOrder: number;
  orderRestrainRangeData: [];
  receiveRangeData: [];
  orderStartTime: string;
  orderEndTime: string;
  orderRestrainStatus: number;
  orderStrokeCount: number;
  orderStrokeNum: number;
  orderRestrainAmount: string;
  orderSkuisExposure: number;
  orderSkuList: any[];
  priceCalculateType: number;
  orderPriceType: number;
  orderStatus: number;
  minOrderPriceType: number;
  minOrderPrice: number;
  // applicationStartTime: string;
  // applicationEndTime: string;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  actBg: '',
  actBgColor: '#c9322b',
  shopNameColor: '#ffffff',
  btnColor: '#c9322b',
  btnBg: '#ffffff',
  btnBorderColor: '#c9322b',
  cutDownBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/198419/29/19494/26997/61ab2722E63bbe6e4/f1e27f2e279f2377.png',
  cutDownColor: '#f2270c',
  cutDownNumBg: '#ffd5cf',
  prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/222223/27/5273/51998/61b167e8Eb7cdbb17/00266549092c6490.png',
  prizeNameColor: '#000000',
  getPrizeBtn: '',
  exchangeBtn: '',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/42367/21/21621/47011/6698fcd8F098f4697/d5cf90562ab781e3.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229461/34/20237/177965/6687bd62F3de20eb2/e5f831aaff0198b7.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/247902/35/15254/6718/6698fceaF5b57703f/5dfd6c79533db3e9.png',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `${dayjs().month() + 1}月消费金额(排名有礼)`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 活动结束生成人群包
    // crowdPackage: 1,
    // 领奖时间
    awardStartTime: '',
    awardEndTime: '',
    receiveRangeData: [],
    awardType: 1, // 奖品领取范围 最高
    // 累计连续签到奖品
    prizeList: [],
    // 是否开启分享
    shareStatus: 1, // 1开启 0关闭
    // 分享标题
    shareTitle: '消费满额，有机会获得大奖哦！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    // 是否延迟发奖
    isDelayedDisttribution: 0,
    // 延迟延迟发奖天数
    awardDays: 0,
    // // 限制订单
    // limitOrder: 1,
    // 限制订单时间
    orderRestrainRangeData: [],
    // 限制订单开始时间
    orderStartTime: '',
    // 限制订单结束时间
    orderEndTime: '',
    // 限制订单状态
    orderRestrainStatus: 1,
    // 限制1：单笔 2：多笔
    orderStrokeCount: 1,
    // 订单笔数
    orderStrokeNum: 1,
    // 限制订单金额
    orderRestrainAmount: '0.00',
    // 订单商品全点商品或指定商品
    orderSkuisExposure: 0,
    // 限制订单商品列表
    orderSkuList: [],
    // 订单金额计算方式
    priceCalculateType: 1,
    // 订单价格结算方式
    orderPriceType: 1,
    //   报名时间
    // applicationStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // applicationEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    orderStatus: 1, // 1订单完成
    minOrderPriceType: 1, // 最低额度限制(1-不限制;2-限制)
    minOrderPrice: 0, // 最低额度限制金额
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  signDay: '',
  ifPlan: 0,
  // 获奖消费额度
  orderPrice: 0,
  sendPeople: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.awardEndTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间应大于领奖结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {
  if (!formData.prizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  for (const index in formData.prizeList) {
    console.log(index);
    const item = formData.prizeList[index];
    if (!item.prizeName) {
      Message.error('请设置奖品');
      return false;
    }
  }
  return true;
};

const hasReceiveStartTime = (formData: PageData): boolean => {
  if (!formData.awardStartTime || !formData.awardEndTime) {
    Message.error('请设置领奖开始时间');
    return false;
  }
  return true;
};
// 获奖排名方法
export const calculatePrizeRank = (index, prizeList): string => {
  const currentPrize = prizeList[index];
  const currentPrizeCount = currentPrize?.sendTotalCount;

  if (index === 0 && currentPrizeCount) {
    return currentPrizeCount === 1 ? `1` : `1-${currentPrizeCount}`;
  } else {
    const totalPrizeCountUntilCurrent = prizeList
      .slice(0, index + 1)
      .reduce((total, prize) => total + (prize?.sendTotalCount || 0), 0);

    const rankStart = totalPrizeCountUntilCurrent - currentPrizeCount + 1;
    if (rankStart === totalPrizeCountUntilCurrent) {
      return `${rankStart}`;
    }
    return currentPrizeCount ? `${rankStart}-${totalPrizeCountUntilCurrent}` : '';
  }
};
export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择奖品累计/连续奖品设置
  if (!hasPrize(formData)) {
    return false;
  }
  // 没有领奖开始时间
  if (!hasReceiveStartTime(formData)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(formData.prizeList, formData)) {
    return false;
  }
  return true;
};
