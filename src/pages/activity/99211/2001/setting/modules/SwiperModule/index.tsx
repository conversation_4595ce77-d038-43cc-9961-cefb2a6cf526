import React from "react";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import { Card, Button, Divider, Message, Loading, Table, Dialog } from "@alifd/next";
import LzDialog from '@/components/LzDialog'
import SwiperItem from '../../compoonets/SwiperSettings'


export default ({ data, dispatch, id }) => {
  const [loading, setLoading] = React.useState(false);
  const [visible, setVisible] = React.useState(false);
  const [editValue, setEditValue] = React.useState(null);
  const [editIndex, setEditIndex] = React.useState(0);
  const onSubmit =(val,type) => {
    const updatedData = { ...data };
    if (type === 'add'){
      updatedData.swiperList.push(val);
      setData(updatedData);
    }else {
      updatedData.swiperList[editIndex] = val;
      setData(updatedData);
    }
  };

  const saveValidate = () => {
    if(data.swiperList.length === 0){
      Message.error(`请至少添加一个轮播图`);
      return false;
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!saveValidate()) {
      return false;
    }
    setLoading(true);
    Message.success(`${data.name}保存成功`);
    dispatch({ type: "RESET_PUSH" });
    setLoading(false);
  };
  const setData = (value) => {
    dispatch({ type: "UPDATE_MODULE", payload: value });
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存模块设置
        </Button>
      </div>
    )
  };

  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} inline={false}>
            <div className={styles.operation}>
              <div className={styles.MemberContainer} key={`member`}>
                <div className="crm-label">背景图设置</div>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      bgWidth={200}
                      bgHeight={140}
                      width={750}
                      height={760}
                      value={data.pageBg}
                      onChange={(pageBg) => {
                        const updatedData = { ...data };
                        updatedData.pageBg = pageBg;
                        setData(updatedData);
                      }}
                    />
                    <div className={styles.tip}>
                      <div>图片尺寸：建议为750px*760px</div>
                      <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className={styles.MemberContainer} key={`member`}>
                <div className="crm-label">轮播项设置</div>
                <Button type={"primary"}
                        style={{marginBottom: "10px"}}
                        disabled={data.swiperList.length >= 5}
                        onClick={() => {
                          setEditValue(null);
                          setVisible(true);
                         }}>
                    增加轮播项（{data.swiperList?.length || 0}/5）
                 </Button>
                  <Table
                    dataSource={data.swiperList}
                    fixedHeader
                    maxBodyHeight={500}>
                    <Table.Column title="图片"
                                  dataIndex="prizeName"
                                  align={"center"}
                                  width={140}
                                  cell={(value, index, record) => {
                                    return (
                                      <div>
                                        <img style={{width: '100px', height: '100px'}}  src={record.img} alt={''}/>
                                      </div>
                                    );
                                  }} />
                    <Table.Column
                      title="跳转链接"
                      align={"center"}
                      cell={(value, index, record) => {
                        return (
                          <div>
                            {record.link}
                          </div>
                        );
                      }} />
                    <Table.Column
                      title="操作"
                      align={"center"}
                      width={140}
                      cell={(value, index, record) => {
                        return (
                          <div style={{display:'flex',justifyContent:'space-between'}}>
                            <Button onClick={() => {
                              setEditValue(record);
                              setVisible(true);
                              setEditIndex(index)
                            }}>
                              编辑
                            </Button>
                            <Button type="primary" onClick={() => {
                              Dialog.confirm({
                                title: "删除",
                                content: "确定删除该轮播吗？",
                                onOk: () => {
                                  if (data.swiperList.length === 1) {
                                    Message.error("至少需要一个轮播配置");
                                    return;
                                  }
                                  const updatedData = { ...data };
                                  updatedData.swiperList.splice(index, 1);
                                  setData(updatedData);
                                }
                              });
                            }}>删除</Button>
                          </div>
                        );
                      }} />
                  </Table>
              </div>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={'新增'}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '450px' }}
      >
        <SwiperItem
          suggestSize={{ height: 722, width: 716 }}
          size={{ height: 722, width: 716 }}
          editValue={editValue}
          onSubmit={onSubmit}
          onClose={() => setVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
