import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button, Message, Select } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataTokenLogRecord, dataTokenLogRecordExport } from '@/api/v99001';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import styles from '../style.module.scss';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const OPTION_TYPE = [
  { label: '全部', value: '' },
  { label: '自动加入', value: 0 },
  { label: '自动移除', value: 1 },
  { label: '手动加入', value: 2 },
  { label: '手动移除', value: 3 },
];
const operateTypeObj = {
  0: '自动加入',
  1: '自动移除',
  2: '手动加入',
  3: '手动移除',
};

export default () => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    if (query.dateRange.length) {
      query.dateRange[0] = format.formatDateTimeDayjs(query.dateRange[0]);
      query.dateRange[1] = format.formatDateTimeDayjs(query.dateRange[1]);
    }
    query.orderStartTime = query.dateRange ? query.dateRange[0] : '';
    query.orderEndTime = query.dateRange ? query.dateRange[1] : '';
    dataTokenLogRecord(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataTokenLogRecordExport(formValue).then((data: any) => downloadExcel(data, '令牌记录'));
  };

  const encryptStr = (pin: string): string => {
    const first = pin.slice(0, 1);
    const last = pin.slice(-1);
    return `${first}****${last}`;
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="encryptPin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="dateRange" label="操作时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem name="operateType" label="操作类型" requiredMessage="请选择操作类型">
          <Select
            followTrigger
            mode="single"
            defaultValue=""
            showSearch
            hasClear
            style={{ marginRight: 8 }}
            dataSource={OPTION_TYPE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
          <Button onClick={exportData}>令牌有效用户导出</Button>
        </FormItem>
      </Form>
      <Table dataSource={tableData} loading={loading} primaryKey="">
        <Table.Column title="序号" dataIndex="num" />
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value, index, data) => (
            <div className={styles.inARow}>
              <div className={styles.copyText}>{data.nickName ? data.nickName : '-'}</div>
              {data.nickName && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    Utils.copyText(data.nickName).then(() => {
                      Message.success('用户昵称已复制到剪切板');
                    });
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value, index, data) => (
            <div className={styles.inARow}>
              <div className={styles.copyText}>{encryptStr(data.encryptPin)}</div>
              <span
                className={`iconfont icon-fuzhi ${styles.copy}`}
                onClick={() => {
                  Utils.copyText(data.encryptPin).then(() => {
                    Message.success('用户pin已复制到剪切板');
                  });
                }}
              />
            </div>
          )}
        />
        <Table.Column
          title="操作时间"
          dataIndex="createTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.createTime)}</div>}
        />
        <Table.Column
          title="操作类型"
          dataIndex="operateType"
          cell={(value, index, data) => <div>{operateTypeObj[data.operateType]}</div>}
        />
        <Table.Column title="操作原因" dataIndex="operateCause" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
