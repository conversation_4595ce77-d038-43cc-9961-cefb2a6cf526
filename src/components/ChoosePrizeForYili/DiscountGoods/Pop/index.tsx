import React, { useState } from 'react';
import styles from './index.module.scss';
import Select from './Select';
import { Dialog, Table, Button } from '@alifd/next';
import dayjs from 'dayjs';
import LzImg from '@/components/LzImg';
import CONST from '@/utils/constant';

export default (props) => {
  const [show, setShow] = useState(false);
  const { submit, value, onlyOne, disabled } = props;
  // const { submit, value, disabled } = props;
  const onSubmit = (prizeInfo) => {
    setShow(false);
    prizeInfo.prizeType = 5;
    submit(prizeInfo);
  };

  // const removeProduct = (index) => {
  //   value.skuInfos.splice(index, 1);
  //   if (value.skuInfos.length) {
  //     submit(value);
  //   } else {
  //     submit(null);
  //   }
  // };
  const removeProduct = () => {
    submit(null);
  };
  return (
    <div>
      {!value && (
        <div className={styles.container} onClick={() => setShow(true)}>
          点击选择折扣商品
        </div>
      )}
      {!!value && (
        <div>
          <div className={styles.headerTitle}>
            <div>
              <span>折扣权益名称：{value.planName}</span>
              <span>投放时间起：{dayjs(value.beginTime).format('YYYY-MM-DD HH:mm:ss')}</span>
              <span>止：{dayjs(value.endTime).format('YYYY-MM-DD HH:mm:ss')}</span>
              <span>可享受此优惠的最大人数：{value.quantityRemain}</span>
            </div>

            <Button text type={'primary'} onClick={removeProduct} disabled={disabled}>
              <i className={`iconfont icon-shanchu`} />
            </Button>
          </div>

          <Table.StickyLock fixedHeader maxBodyHeight={550} dataSource={value.skuInfos}>
            <Table.Column
              title="商品信息"
              width={260}
              cell={(val, index, record) => (
                <div className={styles.part1} style={{ alignItems: 'center' }}>
                  {record.skuPicture?.indexOf('360buyimg.com') > -1 ? (
                    <LzImg style={{ width: 60, height: 60, marginRight: '5px' }} src={record.skuPicture} />
                  ) : (
                    <LzImg
                      style={{ width: 60, height: 60, marginRight: '5px' }}
                      src={`${CONST.IMAGE_PREFIX}${record.skuPicture}`}
                    />
                  )}
                  <div>
                    <div className={styles.part1_p1}>{record.skuName}</div>
                    <div className={styles.part1_p2}>SKU编码：{record.skuId}</div>
                  </div>
                </div>
              )}
            />
            <Table.Column
              title="京东价(元)"
              cell={(val, index, info) => <span>{new Intl.NumberFormat().format(info.jingDongPrice)}</span>}
            />
            <Table.Column
              title="促销价(元)"
              cell={(val, index, info) => <span>{new Intl.NumberFormat().format(info.promoPrice)}</span>}
            />
            {/* <Table.Column title="库存" dataIndex="skuStock" /> */}
            <Table.Column title="售空数量" dataIndex="skuMaxNum" />
            {/* {!disabled && ( */}
            {/*  <Table.Column */}
            {/*    title="操作" */}
            {/*    cell={(val, index, info) => ( */}
            {/*      <Button text type="primary" onClick={() => removeProduct(index)}> */}
            {/*        删除 */}
            {/*      </Button> */}
            {/*    )} */}
            {/*  /> */}
            {/* )} */}
          </Table.StickyLock>
        </div>
      )}

      <Dialog title="选择折扣商品" footer={false} shouldUpdatePosition visible={show} onClose={() => setShow(false)}>
        <Select onlyOne={onlyOne} submit={onSubmit} />
      </Dialog>
    </div>
  );
};
