/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Message, NumberPicker } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import YiLiChoosePrize from '@/components/YiLiChoosePrize';
import { activityEditDisabled, deepCopy, getParams, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';
import { getPrizeRemain } from '@/api/v96077';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [couponVisible, setCouponVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [multipleVisible, setMultipleVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onCouponPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.couponPrizeList[target] = data;
    // 计算总概率
    formData.totalProbability = formData.couponPrizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+formData.totalProbability * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      formData.couponPrizeList.splice(target, 1, PRIZE_INFO);
      return false;
    }
    setData(formData);
    setCouponVisible(false);
  };
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.prizeList[target] = data;
    // 计算总概率
    formData.totalProbability = formData.prizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+formData.totalProbability * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      formData.prizeList.splice(target, 1, PRIZE_INFO);
      return false;
    }
    setData(formData);
    setVisible(false);
  };
  const onMultiplePrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.multiplePrizeList[target] = data;
    // 计算总概率
    formData.totalProbability = formData.multiplePrizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+formData.totalProbability * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      formData.multiplePrizeList.splice(target, 1, PRIZE_INFO);
      return false;
    }
    setData(formData);
    setMultipleVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const setPrizes = () => {};
  useEffect((): void => {
    // 初始奖品列表长度
    const couponPrizeListLength = formData.couponPrizeList.length;
    const prizeListLength = formData.prizeList.length;
    const multiplePrizeListLength = formData.multiplePrizeList.length;
    // 生成默认奖品列表
    const list1: PrizeInfo[] = [...formData.couponPrizeList];
    const list2: PrizeInfo[] = [...formData.prizeList];
    const list3: PrizeInfo[] = [...formData.multiplePrizeList];
    if (!couponPrizeListLength) {
      list1.push(deepCopy(PRIZE_INFO));
    }
    if (!prizeListLength) {
      list2.push(deepCopy(PRIZE_INFO));
    }
    for (let i = 0; i < 2 - multiplePrizeListLength; i++) {
      list3.push(deepCopy(PRIZE_INFO));
    }
    setData({
      couponPrizeList: list1.length ? list1 : formData.couponPrizeList,
      prizeList: list2.length ? list2 : formData.prizeList,
      multiplePrizeList: list3.length ? list3 : formData.multiplePrizeList,
    });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCouponCancel = (): void => {
    setCouponVisible(false);
  };
  const onCancel = (): void => {
    setVisible(false);
  };
  const onMultipleCancel = (): void => {
    setMultipleVisible(false);
  };

  const setExchangeImg = (index, data) => {
    formData.prizeList[index].exchangeImg = data;
    setData(formData);
  };

  const editPrizes = async (prize: any, index: number) => {
    let row = prize[index];
    if (row.prizeName === '谢谢参与' || !row.prizeName) {
      row = null;
    } else if (getParams('type') === 'edit') {
      try {
        const data = await getPrizeRemain({
          prizeType: row.prizeType,
          activityId: getParams('id'),
          prizeKey: row.prizeKey,
        });
        const keyMap = {
          1: 'quantityRemain',
          2: 'quantityRemain',
          3: 'quantityAvailable',
          6: 'quantityRemain',
          7: 'cardSurplus',
          8: 'quantityRemain',
          9: 'quantityRemain',
          10: 'quantityRemain',
          12: 'quantityRemain',
        };
        row[keyMap[row.prizeType]] = +data >= 0 ? +data : 0;
      } catch (error) {
        console.error(error);
      }
    }
    setEditValue(row || null);
    setTarget(index);
  };

  // 编辑会员首单专享券奖品
  const editCouponPrize = async (index: number) => {
    await editPrizes(formData.couponPrizeList, index);
    setCouponVisible(true);
  };

  // 编辑首购权益1奖品
  const editPrizeList = async (index: number) => {
    await editPrizes(formData.prizeList, index);
    setVisible(true);
  };

  // 编辑首购权益2奖品
  const editMultiplePrize = async (index: number) => {
    await editPrizes(formData.multiplePrizeList, index);
    setMultipleVisible(true);
  };

  return (
    <div>
      <LzPanel title="奖项设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="会员首单专享券设置" required>
            <Table dataSource={formData.couponPrizeList}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem disabled={isDisableSetPrize(formData.couponPrizeList, index)}>
                    <Button text type="primary" onClick={() => editCouponPrize(index)}>
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {!activityEditDisabled() && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            if (_.prizeType) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空该奖品？',
                                onOk: () => {
                                  formData.couponPrizeList.splice(index, 1, PRIZE_INFO);
                                  formData.totalProbability = formData.couponPrizeList
                                    .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
                                    .reduce((v, total) => {
                                      return v + Number(total.probability);
                                    }, 0);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                        {formData.couponPrizeList.length > 0 && index > 0 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.couponPrizeList.splice(
                                index - 1,
                                1,
                                ...formData.couponPrizeList.splice(index, 1, formData.couponPrizeList[index - 1]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-35`} />
                          </Button>
                        )}
                        {formData.couponPrizeList.length > 0 && index < formData.couponPrizeList.length - 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.couponPrizeList.splice(
                                index,
                                1,
                                ...formData.couponPrizeList.splice(index + 1, 1, formData.couponPrizeList[index]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-34`} />
                          </Button>
                        )}
                      </>
                    )}
                  </FormItem>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="首购权益1设置" required>
            <Table dataSource={formData.prizeList}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="兑换流程图"
                cell={(_, index, row) =>
                  row.prizeType === 7 ? (
                    <>
                      <Form.Item disabled={false} style={{ marginRight: 10, marginBottom: 0 }}>
                        <LzImageSelector
                          disabled={false}
                          value={row.exchangeImg}
                          onChange={(exchangeImg: string) => {
                            setExchangeImg(index, exchangeImg);
                          }}
                        />
                      </Form.Item>
                    </>
                  ) : (
                    <div>--</div>
                  )
                }
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem disabled={isDisableSetPrize(formData.prizeList, index)}>
                    <Button text type="primary" onClick={() => editPrizeList(index)}>
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {!activityEditDisabled() && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            if (_.prizeType) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空该奖品？',
                                onOk: () => {
                                  formData.prizeList.splice(index, 1, PRIZE_INFO);
                                  formData.totalProbability = formData.prizeList
                                    .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
                                    .reduce((v, total) => {
                                      return v + Number(total.probability);
                                    }, 0);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                        {formData.prizeList.length > 0 && index > 0 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.prizeList.splice(
                                index - 1,
                                1,
                                ...formData.prizeList.splice(index, 1, formData.prizeList[index - 1]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-35`} />
                          </Button>
                        )}
                        {formData.prizeList.length > 0 && index < formData.prizeList.length - 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.prizeList.splice(
                                index,
                                1,
                                ...formData.prizeList.splice(index + 1, 1, formData.prizeList[index]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-34`} />
                          </Button>
                        )}
                      </>
                    )}
                  </FormItem>
                )}
              />
            </Table>
          </FormItem>
          <FormItem
            label="首购权益2设置"
            required
            extra={
              <div className="next-form-item-help">
                提示：用户订单满足上述条件后获得领奖资格，领取奖励后，待订单完成，系统将自动发放奖励
              </div>
            }
          >
            <Table dataSource={formData.multiplePrizeList}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem disabled={isDisableSetPrize(formData.multiplePrizeList, index)}>
                    <Button text type="primary" onClick={() => editMultiplePrize(index)}>
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {!activityEditDisabled() && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            if (_.prizeType) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空该奖品？',
                                onOk: () => {
                                  formData.multiplePrizeList.splice(index, 1, PRIZE_INFO);
                                  formData.totalProbability = formData.multiplePrizeList
                                    .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
                                    .reduce((v, total) => {
                                      return v + Number(total.probability);
                                    }, 0);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                        {formData.multiplePrizeList.length > 0 && index > 0 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.multiplePrizeList.splice(
                                index - 1,
                                1,
                                ...formData.multiplePrizeList.splice(index, 1, formData.multiplePrizeList[index - 1]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-35`} />
                          </Button>
                        )}
                        {formData.multiplePrizeList.length > 0 && index < formData.multiplePrizeList.length - 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.multiplePrizeList.splice(
                                index,
                                1,
                                ...formData.multiplePrizeList.splice(index + 1, 1, formData.multiplePrizeList[index]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-34`} />
                          </Button>
                        )}
                      </>
                    )}
                  </FormItem>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="权益2最大领取份数" required requiredMessage="请输入权益2最大领取份数">
            <NumberPicker
              name="receiveNum"
              min={1}
              max={2}
              type="inline"
              value={formData.receiveNum}
              onChange={(receiveNum: number) => setData({ receiveNum })}
            />{' '}
            份
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={couponVisible}
        footer={false}
        onClose={() => setCouponVisible(false)}
        style={{ width: '670px' }}
      >
        <YiLiChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onCouponPrizeChange}
          typeList={[1, 4]}
          defaultTarget={1}
          onCancel={onCouponCancel}
          hasLimit={false}
          hasProbability={false}
        />
      </LzDialog>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <YiLiChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          typeList={[3, 4, 7]}
          defaultTarget={3}
          onCancel={onCancel}
          hasLimit={false}
          hasProbability={false}
        />
      </LzDialog>

      <LzDialog
        title={false}
        visible={multipleVisible}
        footer={false}
        onClose={() => setMultipleVisible(false)}
        style={{ width: '670px' }}
      >
        <YiLiChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onMultiplePrizeChange}
          typeList={[2, 4, 6]}
          defaultTarget={2}
          onCancel={onMultipleCancel}
          hasLimit={false}
          hasProbability={false}
        />
      </LzDialog>
    </div>
  );
};
