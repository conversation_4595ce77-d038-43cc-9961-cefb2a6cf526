/**
 * Author: liangyu
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Form, Table, Input, Dialog, Grid } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [goodDialog, setGoodDialog] = useState(false);
  const [currentGood, setCurrentGood] = useState<any>({});

  formItemLayout.labelAlign = labelAlign;
  const showGoods = (good): void => {
    console.log(good, '商品展示');
    setCurrentGood(good);
    setGoodDialog(true);
  };

  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>

        <FormItem label="付款时间设置">
          <div>
            预售付定金时间：
            {`${format.formatDateTimeDayjs(formData.payEarnestStartTime)}至${format.formatDateTimeDayjs(
              formData.payEarnestEndTime,
            )}`}
          </div>
          <div>
            预售付尾款时间：
            {`${format.formatDateTimeDayjs(formData.payBalanceStartTime)}至${format.formatDateTimeDayjs(
              formData.payBalanceEndTime,
            )}`}
          </div>
        </FormItem>

        <FormItem label="赠品设置" isPreview={false}>
          {formData.giveawayList.map((items, index1) => {
            return (
              <div key={index1}>
                <div {...formItemLayout} key={index1}>
                  <div className={styles.awardTitle}>奖励{index1 + 1}</div>
                  <div>
                    <div className={styles.skuListDiv}>
                      <div className={styles.labelTitle}>指定商品：</div>
                      <div style={{ marginLeft: '8px' }}>
                        <div style={{ color: '#1677ff', cursor: 'pointer' }} onClick={() => showGoods(items)}>
                          商品展示
                        </div>
                        <div>
                          <p className={styles.tip}>注：只可选择1个商品，且不可选影分身商品</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.skuImageDiv}>
                    <div className={styles.labelTitle}>上传商品图：</div>
                    <Grid.Row style={{ marginLeft: '8px' }}>
                      <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                        <img className={styles.skuListImage} src={items.giveawayImage} alt="" />
                      </Form.Item>
                    </Grid.Row>
                  </div>
                  <div className={styles.prizeListDiv}>
                    <div className={styles.labelTitle}>奖品列表：</div>
                    <div style={{ marginLeft: '8px' }}>
                      <Table dataSource={items.prizeList} style={{ marginTop: '15px' }}>
                        <Table.Column
                          title="设置订单排名"
                          dataIndex="prizeName"
                          width={150}
                          cell={(_, index, row) => (
                            <div>第1名至第{row.sendTotalCount > 0 ? row.sendTotalCount : 'N'}名</div>
                          )}
                        />
                        <Table.Column title="奖品名称" dataIndex="prizeName" />
                        <Table.Column
                          title="奖品类型"
                          cell={(_, index, row) => (
                            <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                              {PRIZE_TYPE[row.prizeType]}
                            </div>
                          )}
                          dataIndex="prizeType"
                        />
                        <Table.Column
                          title="单位数量"
                          cell={(_, index, row) => {
                            if (row.prizeType === 1) {
                              return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                            } else {
                              return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                            }
                          }}
                        />
                        <Table.Column
                          title="发放总数"
                          cell={(_, index, row) => (
                            <div>
                              {Number(row.sendTotalCount) * Number(row.unitCount)
                                ? `${Number(row.sendTotalCount) * Number(row.unitCount)}份`
                                : ''}
                            </div>
                          )}
                        />
                        <Table.Column
                          title="单份价值(元)"
                          cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
                        />
                        <Table.Column
                          title="奖品图"
                          cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                        />
                      </Table>
                      <p className={styles.tip}>
                        注：点击编辑，选择发放份数后，自动同步名次。例如：选择发放份数为1000，订单排名为第1名-第1000名
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </FormItem>

        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>

      <Dialog
        width={822}
        v2
        title="查看商品"
        visible={goodDialog}
        footer={false}
        onClose={() => setGoodDialog(false)}
        onOk={() => setGoodDialog(false)}
      >
        <div className={styles.container}>
          {currentGood.skuList?.map((sku, index11) => {
            return (
              <div key={index11} className={styles.skuContainer}>
                <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
                <div>
                  <div className={styles.skuName}>{sku.skuName}</div>
                  <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                  <div className={styles.price}>¥ {sku.jdPrice}</div>
                </div>
              </div>
            );
          })}
        </div>
      </Dialog>
    </div>
  );
};
