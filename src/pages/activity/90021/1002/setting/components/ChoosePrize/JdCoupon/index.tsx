import { Radio, Form, Button, NumberPicker, Input, Dialog, Grid, Field, Message } from '@alifd/next';
import React, { useState, useReducer, useEffect } from 'react';
import Plan from './components/Plan';
import active from '../assets/active.png';
import notActive from '../assets/not-active.png';
import delIcon from '../assets/del-icon.png';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import format from '@/utils/format';
import { prizeFormLayout } from '@/components/ChoosePrize';
import { activityEditDisabled, getParams } from '@/utils';

interface ComponentProps {
  [propName: string]: any;
}

// eslint-disable-next-line complexity
const PropertyJdCoupon = ({
  editValue,
  onChange,
  onCancel,
  hasProbability = true,
  hasLimit = true,
  hasOrderPrice = false,
  width,
  height,
  prizeNameLength,
  formData,
  sendTotalCountMax = 999999999,
}: ComponentProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/215393/22/4731/33329/61946651E535ea01f/5cee5951d6bd1612.png';
  const planImg = require('../assets/3.jpg');
  const defaultValue = {
    ifPlan: 1,
    prizeKey: null,
    prizeType: 1,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
  };

  // 保存原始的editValue中的sendTotalCount值
  const originalSendTotalCount = editValue?.sendTotalCount || 1;

  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  // 检查是否处于编辑模式
  const isEditMode = getParams('type') === 'edit';
  // 在编辑模式下，发放份数的最小值应该是原始的editValue中的值
  const minSendTotalCount = isEditMode ? originalSendTotalCount : 1;

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): boolean | void => {
    // 剩余数量 / 每份发放份数
    const dis = Math.floor(prizeData.quantityRemain / prizeData.numPerSending);
    if (prizeData.sendTotalCount > dis) {
      Message.error(`发放份数不能大于剩余份数：${dis}`);
      return false;
    }
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    // if (hasProbability && +prizeData.probability <= 0) {
    //   Message.error(`中奖概率必须大于0`);
    //   return false;
    // }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100%`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    data.prizeName = '';
    setPrizeData(data);
  };
  const onSubmit = (resource: any) => {
    if (!resource) {
      return;
    }
    resource.prizeKey = resource.planId;
    resource.prizeName = resource.planName.substring(0, prizeNameLength);
    setPrizeData(resource);
    setWinJdShow(false);
    field.setErrors({ prizeKey: '', prizeName: '' });
  };
  return (
    <div className={styles.PropertyJdCoupon}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item required requiredMessage="请选择优惠券" style={{ paddingTop: '15px' }}>
          <Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={prizeData.planStatus === 2 ? styles.beanPrizeBg : styles.beanPrizeBgy}>
              <img className={styles.prizeBg} src={prizeData.planStatus === 2 ? active : notActive} alt="" />
              {!activityEditDisabled() && (
                <div
                  onClick={() => delPlan()}
                  style={{ backgroundImage: `url(${delIcon})` }}
                  className={styles.delIcon}
                />
              )}
              <div style={{ width: 210 }}>
                <p>优惠券名称：{prizeData.planName}</p>
                <p>优惠券类型：{prizeData.rangeType == 1 ? '店铺券' : '商品券'}</p>
                <p style={{ whiteSpace: 'nowrap' }}>
                  优惠券信息：
                  {prizeData.couponType ? `东券,满${prizeData.couponQuota}元减${prizeData.couponDiscount}元` : '京券'}
                </p>
                <p>每份数量：{prizeData.numPerSending || 1}张</p>
              </div>
              <div style={{ paddingLeft: 60 }}>
                <p>
                  领取时间：{format.formatDateTimeDayjs(prizeData.startTime)}至
                  {format.formatDateTimeDayjs(prizeData.endTime)}
                </p>
                {/* <p>限领条件：{prizeData.takeRule === 4 ? '活动期内每天限领一张' : '活动内限领一张'}</p> */}
                <p>剩余张数：{prizeData.quantityRemain}张</p>
                <p>剩余份数：{Math.floor(prizeData.quantityRemain / prizeData.numPerSending) || 1}份</p>
              </div>
            </div>
          )}
        </Form.Item>
        <div style={{ color: 'red', marginTop: '15px', marginBottom: '15px' }}>
          注意：奖品名称&奖品图将展示于C端页面中，包含：领取记录、新客礼1/老客礼1领取成功页面、新客礼2权益4选1页面、新客礼2领取成功页面等；活动开始后将无法修改，仅可增加奖品份数，无法减少，请谨慎填写。
        </div>
        <Form.Item label="单份价值" required requiredMessage="请输入单份价值">
          <NumberPicker
            onChange={(unitPrice: any) => setData({ unitPrice })}
            placeholder="请输入单份价值"
            className={styles.formNumberPicker}
            type="inline"
            name="unitPrice"
            min={0.01}
            precision={2}
            max={9999999}
            value={prizeData.unitPrice}
            disabled={activityEditDisabled()}
          />
          元
        </Form.Item>
        {hasOrderPrice && (
          <Form.Item label="获奖消费额度" required requiredMessage="请输入获奖消费额度">
            <NumberPicker
              className={styles.formNumberPicker}
              placeholder="请输入获奖消费额度"
              onChange={(orderPrice: any) => setData({ orderPrice })}
              name="orderPrice"
              type="inline"
              min={0}
              max={9999999}
              precision={2}
              value={prizeData.orderPrice}
            />
            元
          </Form.Item>
        )}
        <Form.Item
          label="发放份数"
          required
          requiredMessage="请输入发放份数"
          extra={<div style={{ color: 'red' }}>注：发放份数指本次投入活动中优惠券份数</div>}
        >
          <NumberPicker
            className={styles.formNumberPicker}
            placeholder="请输入发放份数"
            type="inline"
            min={minSendTotalCount}
            max={sendTotalCountMax}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount, unitCount: 1 })}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              placeholder="请输入中奖概率"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />

            <div style={{ marginTop: 5 }}>中奖概率不建议为0%，易引发客诉，请慎重</div>
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType}
                onChange={(dayLimitType) => setData({ dayLimitType, dayLimit: dayLimitType === 1 ? 0 : '' })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    value={prizeData.dayLimit}
                    placeholder="请输入每日限额"
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
            disabled={activityEditDisabled()}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择优惠券"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} />
      </Dialog>
    </div>
  );
};
export default PropertyJdCoupon;
