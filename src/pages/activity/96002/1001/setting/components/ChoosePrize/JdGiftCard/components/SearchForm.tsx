import * as React from 'react';
import { Form, Input, DatePicker2, Field } from '@alifd/next';

export default ({ onSearch }: any) => {
  const rangeDate = [];
  const { RangePicker } = DatePicker2;
  const field = Field.useField();

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    console.log(formValue);
    onSearch(formValue);
  };

  const onResetClick = () => {
    const formValue: any = field.getValues();
    onSearch(formValue);
  };
  return (
    <div>
      <Form inline field={field} onSubmit={handleSubmit}>
        <Form.Item className="item" label="礼品卡名称：" name="cardName">
          <Input className="dialog-search-ctrl" placeholder="请输入礼品卡名称" />
        </Form.Item>
        <Form.Item className="item" label="礼品卡code：" name="cardCode">
          <Input className="dialog-search-ctrl" placeholder="请输入礼品卡code" />
        </Form.Item>
        <Form.Item className="item" name="dateRange" label="创建时间：">
          <RangePicker
            className="w-300"
            inputReadOnly
            showTime
            hasClear={false}
            defaultValue={rangeDate}
            format="YYYY-MM-DD HH:mm:ss"
            name="rangeDate"
          />
        </Form.Item>
        <Form.Item className="item" style={{ textAlign: 'right' }}>
          <Form.Submit type="primary" htmlType="submit" style={{ marginRight: '4px' }}>
            查询
          </Form.Submit>
          <Form.Reset toDefault onClick={onResetClick}>
            重置
          </Form.Reset>
        </Form.Item>
      </Form>
    </div>
  );
};
