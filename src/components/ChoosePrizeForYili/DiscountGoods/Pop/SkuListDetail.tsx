import React, { useEffect, useState } from 'react';
import { Table, Balloon, Icon } from '@alifd/next';
import LzSku from '@/components/LzSku';

export default ({ skuData }) => {
  console.log('skuData', skuData);
  const [skuList, setSkuList] = useState([]);
  const defaultTrigger = <Icon size="small" type="help" />;

  const saleShouKongTitle = () => {
    return (
      <div>
        售空数量 &nbsp;&nbsp;
        <Balloon trigger={defaultTrigger} closable={false}>
          此处的&quot;售空数量&quot;是指本次活动每个SKU可优惠购买的总数量
        </Balloon>
      </div>
    );
  };

  useEffect(() => {
    setSkuList(skuData.skuInfos);
  });

  return (
    <Table.StickyLock maxBodyHeight={500} fixedHeader dataSource={skuList}>
      <Table.Column width={135} title="SKUID" dataIndex="skuId" />
      <Table.Column
        title="SKU商品名称"
        cell={(val, index, record) =>
          LzSku({
            logo: record.skuPicture,
            title: record.skuName,
            style: { alignItems: 'center' },
          })
        }
      />
      <Table.Column
        width={100}
        title="京东价"
        cell={(value, index, tableData) => (
          <div>
            <span>￥{new Intl.NumberFormat().format(tableData.jingDongPrice)}</span>
          </div>
        )}
      />
      <Table.Column
        width={100}
        title="打折"
        cell={(value, index, tableData) => (
          <div>
            <span>{((+tableData.promoPrice / +tableData.jingDongPrice) * 10).toFixed(2)}</span>
          </div>
        )}
      />
      <Table.Column
        width={100}
        title="促销价"
        cell={(value, index, tableData) => (
          <div>
            <span>￥{new Intl.NumberFormat().format(tableData.promoPrice)}</span>
          </div>
        )}
      />
      <Table.Column width={120} title={saleShouKongTitle} dataIndex="skuMaxNum" />
      {/* <Table.Column width={100} title="库存" dataIndex="skuStock" /> */}
    </Table.StickyLock>
  );
};
