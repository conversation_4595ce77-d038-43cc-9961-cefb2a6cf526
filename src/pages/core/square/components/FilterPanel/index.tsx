import { Radio } from '@alifd/next';
import React, { useState } from 'react';
import styles from './style.module.scss';
import { initData } from '@/models/squarer';

const strategyDatasource = ['全部', '用户招募', '用户活跃', '用户转化', '增加权重', '多任务'];
const tagsDatasource = [
  '全部',
  '抽奖',
  '签到',
  '加购',
  '下单',
  '关注',
  '入会',
  '分享和裂变',
  '游戏',
  '分享',
  '评价',
  '官方',
  '话题和投票',
  '问卷和答题',
];

const sortDatasource = ['全部', '最新活动', '最热活动'];

export default ({ onChange, squarer }) => {
  if (!squarer) squarer = initData;

  const [strategy, setStrategy] = useState(squarer.strategy);
  const [tag, setTag] = useState(squarer.tag);
  const [sort, setSort] = useState(squarer.sort);

  const onStrategyChange = (value) => {
    setStrategy(value);
    onChange({
      strategy: value,
      tag,
      sort,
    });
  };
  const onTagChange = (value) => {
    setTag(value);
    onChange({
      strategy,
      tag: value,
      sort,
    });
  };
  const onSortChange = (value) => {
    setSort(value);
    onChange({
      strategy,
      tag,
      sort: value,
    });
  };

  React.useEffect(() => {
    setStrategy(squarer.strategy);
    setTag(squarer.tag);
    setSort(squarer.sort);
  }, [squarer]);

  return (
    <>
      <div className={styles.row}>
        <div className={styles.label}>互动策略：</div>
        <Radio.Group
          className={[styles.radioButton, 'lz-filter-radio-group'].join(' ')}
          dataSource={strategyDatasource}
          shape="button"
          size="small"
          value={strategy}
          onChange={onStrategyChange}
        />
      </div>
      <div className={styles.row}>
        <div className={styles.label}>活动关键字：</div>
        <Radio.Group
          className={[styles.radioButton, 'lz-filter-radio-group'].join(' ')}
          dataSource={tagsDatasource}
          shape="button"
          size="small"
          value={tag}
          onChange={onTagChange}
        />
      </div>
      <div className={styles.row}>
        <div className={styles.label}>排序方式：</div>
        <Radio.Group
          className={[styles.radioButton, 'lz-filter-radio-group'].join(' ')}
          dataSource={sortDatasource}
          shape="button"
          size="small"
          value={sort}
          onChange={onSortChange}
        />
      </div>
    </>
  );
};
