/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import {
  Form,
  Field,
  Table,
  Button,
  Dialog,
  NumberPicker,
  Input,
  Grid,
  Message,
  Checkbox,
  Tab,
  Select,
  Radio,
} from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import { activityEditDisabled, deepCopy, isDisableSetPrize } from '@/utils';
import { FormLayout, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';
// 当前编辑行数据 除资产外 附加信息
// 定义常量
const MAX_PRIZE_LIST_LENGTH = activityEditDisabled() ? 6 : 3;
const MAX_STEP_SETTING_LENGTH = 2;
const MAX_SERIES_PRIZE_LIST_LENGTH = 4;
const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
const RadioGroup = Radio.Group;
export default ({ sRef, onChange, value, defaultValue }) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [editValueMoreInfo, setEditValueMoreInfo] = useState({});
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);
  const [activeKey, setActiveKey] = useState('0');

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // if (!editValue) {
    //   for (let i = 0; i < formData.seriesPrizeList.length; i++) {
    //     const { prizeList } = formData.seriesPrizeList[i];
    //     const hasThisPrize = prizeList.find((item) => item.prizeKey === data.prizeKey);
    //     if (hasThisPrize) {
    //       Message.error('奖品已存在, 请重新选择');
    //       return;
    //     }
    //   }
    // }
    formData.seriesPrizeList[activeKey].prizeList[target] = { ...data, ...editValueMoreInfo };
    setData(formData);
    setVisible(false);
  };

  useEffect((): void => {
    setFormData(value);
  }, [value]);

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      console.log(898);
      let err: object | null = null;
      field.validate((errors: any): void => {
        console.log('errors', errors);
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  const addPrizeSetting = () => {
    formData.seriesPrizeList.push({
      seriesName: '',
      seriesPic: '',
      beforeOptions: [],
      afterOptions: [],
      prizeList: [],
      beforeSkuList: [],
      afterSkuList: [],
      stepSetting: [
        {
          step: 1,
          minPotNum: 1,
          maxPotNum: 5,
          stepImg: '',
        },
      ],
    });
    setData(formData);
  };

  const handleStepSettingChange = (item, index, newValue, isMinPotNum) => {
    const { stepSetting } = item;
    if (isMinPotNum) {
      stepSetting[index].minPotNum = newValue;
      stepSetting[index].maxPotNum = Math.min(newValue + 1, 99);
    } else {
      stepSetting[index].maxPotNum = Math.min(newValue, 99);
      if (index == 0 && stepSetting.length > 1) {
        stepSetting[index + 1].minPotNum = newValue + 1;
      }
    }

    setData(formData);
  };

  const renderStepSetting = (item) => {
    return item.stepSetting.map((row, index) => (
      <div style={{ marginTop: 10 }} key={index}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FormItem label={`阶梯${row.step}`} colon />
          <FormItem label={`罐数`} />
          <FormItem required requiredMessage="请输入最少罐数门槛">
            <NumberPicker
              disabled={activityEditDisabled() || index > 0 || item.stepSetting?.length == MAX_STEP_SETTING_LENGTH}
              min={Math.max(item.stepSetting[index]?.minPotNum, 1)}
              max={99}
              step={1}
              value={row.minPotNum || ''}
              onChange={(minPotNum) => handleStepSettingChange(item, index, minPotNum, true)}
              name={`minPotNum-${index}`}
            />
          </FormItem>
          <FormItem>
            <span style={{ padding: '0 10px' }}>至</span>
          </FormItem>
          <FormItem required requiredMessage="请输入最多罐数门槛">
            <NumberPicker
              disabled={activityEditDisabled()}
              min={Math.max(item.stepSetting[index]?.minPotNum, 1)}
              max={Math.min(item.stepSetting[index + 1]?.minPotNum - 1, 99)}
              step={1}
              value={row.maxPotNum || ''}
              onChange={(maxPotNum) => handleStepSettingChange(item, index, maxPotNum, false)}
              name={`maxPotNum-${index}`}
            />
          </FormItem>
          <FormItem>
            <Button
              style={{ marginLeft: 10 }}
              disabled={activityEditDisabled() || index === 0}
              text
              type="primary"
              onClick={() => handleDeleteStepSetting(item, index)}
            >
              <i className={`iconfont icon-shanchu`} />
            </Button>
          </FormItem>
        </div>
        <div>
          <FormItem required requiredMessage={'请上传阶段奖品图'} label="阶段奖品图" labelAlign={'left'}>
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  value={row?.stepImg}
                  onChange={(stepImg) => {
                    formData.seriesPrizeList[activeKey].stepSetting[index].stepImg = stepImg;
                    setData(formData);
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div>
                  <p>图片尺寸：675px*466px</p>
                  <p>图片大小：不超过32KB</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
              </Form.Item>
            </Grid.Row>
          </FormItem>
        </div>
      </div>
    ));
  };

  // 删除阶梯配置的处理函数
  const handleDeleteStepSetting = (item, index) => {
    if (item.prizeList.find((it) => +it.step === +item.stepSetting[index].step)) {
      return Dialog.confirm({
        v2: true,
        title: '提示',
        centered: true,
        content: '当前阶梯已经被选择，请删除该奖品或更改奖品所属阶梯后再删除。',
        onOk: () => {},
        onCancel: () => {},
      } as any);
    } else {
      Dialog.confirm({
        v2: true,
        title: '提示',
        centered: true,
        content: '确认删除该阶梯配置？',
        onOk: () => {
          item.stepSetting.splice(index, 1);
          item.stepSetting.forEach((it, itemIndex) => {
            it.step = itemIndex + 1;
          });
          setData(formData);
        },
        onCancel: () => console.log('cancel'),
      } as any);
    }
  };

  return (
    <div>
      <LzPanel title="商品及奖品设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Button
            style={{ width: '100%' }}
            type="secondary"
            onClick={addPrizeSetting}
            disabled={
              formData.seriesPrizeList.length >= MAX_SERIES_PRIZE_LIST_LENGTH ||
              !formData.fileList.length ||
              activityEditDisabled()
            }
          >
            增加奖品配置 ({formData.seriesPrizeList.length}/{MAX_SERIES_PRIZE_LIST_LENGTH})
          </Button>
          <Tab
            activeKey={activeKey}
            onChange={(key) => setActiveKey(key)}
            unmountInactiveTabs
            triggerType={'click'}
            onClose={(key) => {
              Dialog.confirm({
                v2: true,
                title: '提示',
                centered: true,
                content: ' 删除该奖品项配置',
                onOk: () => {
                  formData.seriesPrizeList.splice(key, 1);
                  setActiveKey('0');
                  setData(formData);
                },
                onCancel: () => {},
              } as any);
            }}
          >
            {formData.seriesPrizeList.map((item, TabIndex) => {
              return (
                <Tab.Item
                  closeable={activityEditDisabled() ? false : TabIndex > 0}
                  title={` 奖品项${TabIndex + 1}  `}
                  key={TabIndex}
                >
                  <Form {...formItemLayout} field={field}>
                    <FormItem required label="系列名" requiredMessage={'请输入系列名称'}>
                      <Input
                        disabled={activityEditDisabled()}
                        value={item?.seriesName}
                        placeholder="请输入系列名称"
                        name={`seriesName-${TabIndex}`}
                        maxLength={10}
                        showLimitHint
                        className="w-300"
                        onChange={(seriesName) => {
                          item.seriesName = seriesName;
                          setData(formData);
                        }}
                      />
                    </FormItem>
                    <FormItem required requiredMessage={'请上传系列图'} label="系列图">
                      <Grid.Row>
                        <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                          <LzImageSelector
                            value={item?.seriesPic}
                            onChange={(seriesPic) => {
                              item.seriesPic = seriesPic;
                              setData(formData);
                            }}
                          />
                        </Form.Item>
                        <Form.Item style={{ marginBottom: 0 }}>
                          <div>
                            <p>图片尺寸：675px*466px</p>
                            <p>图片大小：不超过32KB</p>
                            <p>图片格式：JPG、JPEG、PNG</p>
                          </div>
                          <div>
                            <Button
                              disabled={activityEditDisabled()}
                              type="primary"
                              text
                              onClick={() => {
                                item.seriesPic = '';
                                setData(formData);
                              }}
                            >
                              重置
                            </Button>
                          </div>
                        </Form.Item>
                      </Grid.Row>
                      <Input className="validateInput" name={`seriesPic-${TabIndex}`} value={item?.seriesPic} />
                    </FormItem>
                    <FormItem required requiredMessage={'请进行阶梯配置'} label="阶梯配置">
                      <Button
                        disabled={activityEditDisabled() || item.stepSetting?.length >= MAX_STEP_SETTING_LENGTH}
                        type="primary"
                        onClick={() => {
                          item.stepSetting.push({
                            step: item.stepSetting?.length + 1,
                            minPotNum: Math.min(item.stepSetting[item.stepSetting?.length - 1].maxPotNum + 1, 99),
                            maxPotNum: Math.min(item.stepSetting[item.stepSetting?.length - 1].maxPotNum + 2, 99),
                          });
                          setData(formData);
                        }}
                      >
                        增加阶梯配置（{item.stepSetting?.length}/{MAX_STEP_SETTING_LENGTH}）
                      </Button>
                      {renderStepSetting(item)}
                    </FormItem>
                    <FormItem
                      name={`beforeOptions-${TabIndex}`}
                      required
                      label="转段前商品"
                      requiredMessage={'请选择转段前商品'}
                    >
                      <Checkbox.Group
                        disabled={activityEditDisabled()}
                        onChange={(beforeOptions) => {
                          console.log('beforeOptions', beforeOptions);
                          item.beforeOptions = beforeOptions;
                          // item.afterOptions = beforeOptions.length > 0 ? item.afterOptions : '';
                          // item.afterOptions =
                          //   beforeOptions.length == formData.seriesList.length ? '' : item.afterOptions;
                          item.afterOptions =
                            Math.max(...item.beforeOptions, 0) >= item.afterOptions ? '' : item.afterOptions;
                          item.beforeSkuList = beforeOptions.reduce((acc, cur) => {
                            const skuList = formData.seriesList.find((option) => option.periodSort === cur)?.skuList;
                            if (skuList) {
                              acc.push(...skuList);
                            }
                            return acc;
                          }, []);
                          setData(formData);
                        }}
                        name={`beforeOptions-${TabIndex}`}
                        value={item.beforeOptions}
                      >
                        {formData.seriesList.map((option, index) => (
                          <Checkbox
                            disabled={index === formData.seriesList.length - 1}
                            key={option.periodSort}
                            value={option.periodSort}
                          >
                            {option.period}
                          </Checkbox>
                        ))}
                        <Button
                          disabled={item.beforeOptions?.length === 0}
                          onClick={() => {
                            setSeriesSkuList(item?.beforeSkuList);
                            setSkuVisible(true);
                          }}
                        >
                          查看SKU
                        </Button>
                      </Checkbox.Group>
                    </FormItem>
                    <FormItem
                      required
                      label="转段后商品"
                      requiredMessage={'请选择转段后商品'}
                      name={`afterOptions-${TabIndex}`}
                    >
                      <RadioGroup
                        disabled={activityEditDisabled()}
                        name={`afterOptions-${TabIndex}`}
                        onChange={(afterOptions) => {
                          item.afterOptions = afterOptions;
                          item.afterSkuList = formData.seriesList.find(
                            (option) => option.periodSort === afterOptions,
                          ).skuList;
                          item.previewSkuList = formData.seriesList.find(
                            (option) => option.periodSort === afterOptions,
                          ).previewSkuList;
                          item.tabName = formData.seriesList.find(
                            (option) => option.periodSort === afterOptions,
                          ).period;
                          setData(formData);
                        }}
                        value={item.afterOptions}
                      >
                        {formData.seriesList.map((option) => (
                          <Radio
                            key={option.periodSort}
                            value={option.periodSort}
                            disabled={
                              item.beforeOptions?.length === 0 ||
                              Math.max(...item.beforeOptions, 0) >= option.periodSort
                            }
                          >
                            {option.period}
                          </Radio>
                        ))}
                        <Button
                          disabled={item.afterOptions?.length === 0}
                          onClick={() => {
                            setSeriesSkuList(item.afterSkuList);
                            setSkuVisible(true);
                          }}
                        >
                          查看SKU
                        </Button>
                      </RadioGroup>
                    </FormItem>
                    <FormItem required label="奖品列表">
                      <FormItem>
                        <Table dataSource={item?.prizeList} style={{ marginTop: '15px' }}>
                          <Table.Column
                            title="所属阶梯"
                            cell={(_, index, row) => (
                              <FormItem required requiredMessage="请选择所属阶梯">
                                <Select
                                  followTrigger
                                  mode="single"
                                  hasClear
                                  name={`step-${index}`}
                                  popupProps={{ autoFit: true }}
                                  value={row.step}
                                  disabled={
                                    activityEditDisabled() && !!defaultValue.seriesPrizeList[TabIndex].prizeList[index]
                                  }
                                  dataSource={item.stepSetting.map((it, stepIndex) => {
                                    return {
                                      label: `阶梯${it.step}`,
                                      value: `${it.step}`,
                                    };
                                  })}
                                  onChange={(val) => {
                                    item.prizeList[index].step = val;
                                    setData(formData);
                                  }}
                                />
                              </FormItem>
                            )}
                          />
                          <Table.Column title="奖品名称" dataIndex="prizeName" />
                          <Table.Column
                            title="奖品类型"
                            cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                            dataIndex="prizeType"
                          />
                          <Table.Column
                            title="单位数量"
                            cell={(_, index, row) => {
                              if (row.prizeType === 1) {
                                return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                              } else {
                                return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                              }
                            }}
                          />
                          <Table.Column
                            title="发放份数"
                            cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}` : ''}</div>}
                          />
                          <Table.Column
                            title="单份价值(元)"
                            cell={(_, index, row) => (
                              <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                            )}
                          />

                          <Table.Column
                            title="操作"
                            width={130}
                            cell={(val, index, _) => (
                              <FormItem>
                                <Button
                                  text
                                  type="primary"
                                  onClick={() => {
                                    let row = item.prizeList[index];
                                    if (row.prizeName === '') {
                                      row = null;
                                    }
                                    setEditValue(row);
                                    setTarget(index);
                                    setEditValueMoreInfo({
                                      step: formData.seriesPrizeList[activeKey].prizeList[index].step,
                                    });
                                    setTableName('seriesPrizeList');
                                    setVisible(true);
                                  }}
                                >
                                  <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                                </Button>
                                {formData.seriesPrizeList[activeKey].prizeList.length > 1 && (
                                  <Button
                                    text
                                    type="primary"
                                    disabled={activityEditDisabled()}
                                    onClick={() => {
                                      Dialog.confirm({
                                        v2: true,
                                        title: '提示',
                                        centered: true,
                                        content: '确认删除该奖品？',
                                        onOk: () => {
                                          item.prizeList.splice(index, 1);
                                          setData(formData);
                                        },
                                        onCancel: () => console.log('cancel'),
                                      } as any);
                                    }}
                                  >
                                    <i className={`iconfont icon-shanchu`} />
                                  </Button>
                                )}
                              </FormItem>
                            )}
                          />
                        </Table>
                      </FormItem>
                      <FormItem>
                        <Button
                          disabled={item?.prizeList?.length >= (activityEditDisabled() ? 6 : 3)}
                          type="primary"
                          onClick={() => {
                            item?.prizeList?.push(deepCopy({ ...PRIZE_INFO }));
                            setData(formData);
                          }}
                        >
                          +添加奖品（{item.prizeList?.length}/{activityEditDisabled() ? 6 : 3}）
                        </Button>
                      </FormItem>
                    </FormItem>
                  </Form>
                </Tab.Item>
              );
            })}
          </Tab>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          defaultEditValue={defaultValue?.seriesPrizeList[activeKey]?.prizeList[target] ?? null}
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
          hasShowTime
          typeList={[2, 3]}
          defaultTarget={2}
        />
      </LzDialog>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="阶段" dataIndex="period" />
              <Table.Column title="罐数" dataIndex="potNum" />
              <Table.Column title="商品id" dataIndex="skuId" />
              <Table.Column
                title="C端是否展示"
                cell={(data, index, row) => {
                  return <div>{row.isShow == 1 ? '是' : '否'}</div>;
                }}
              />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
