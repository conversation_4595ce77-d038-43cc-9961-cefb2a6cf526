/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, NumberPicker, Radio, Grid, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import YiLiChoosePrize from '@/components/YiLiChoosePrize';
import { activityEditDisabled, getParams, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import ChooseGoods from '@/components/ChooseGoods';
import SkuList from '@/components/SkuList';
import styles from '@/pages/activity/96090/2002/setting/style.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { getPrizeRemain } from '@/api/v96090';

const RadioGroup = Radio.Group;
interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [ladderVisible, setLadderVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [multipleVisible, setMultipleVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [plan, setPlan] = useState<any[]>([]);
  const [maxMultipleReceiveNum, setMaxMultipleReceiveNum] = useState(1);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  // const onLadderPrizeChange = (data: any): void => {
  //   // 假设 data 是一个包含 prizeKey 的对象，可能还有其他属性
  //   // 但不一定包含 buyTimes
  //
  //   // 浅拷贝原来的对象，以防外部引用问题
  //   const updatedItem = { ...formData.ladderPrizeList[target], ...data };
  //
  //   // 如果 data 中没有 buyTimes，则保留原来的 buyTimes
  //   if (!('buyTimes' in data)) {
  //     updatedItem.buyTimes = formData.ladderPrizeList[target].buyTimes;
  //   }
  //
  //   // 更新指定index的奖品信息
  //   formData.ladderPrizeList[target] = updatedItem;
  //
  //   // 更新其他逻辑...
  //   const list = formData.ladderPrizeList.map((item) => item.prizeKey);
  //   setPlan(list);
  //   setData(formData); // 假设 setData 是更新全局或父组件状态的方法
  //   setLadderVisible(false); // 隐藏某个视图或弹窗
  // };
  // const onPrizeChange = (data): boolean | void => {
  //   // 更新指定index 奖品信息
  //   formData.registrationPrizeList[target] = data;
  //   const list = formData.ladderPrizeList.map((item) => item.prizeKey);
  //   setPlan(list);
  //   setData(formData);
  //   setVisible(false);
  // };
  const onMultiplePrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.multiplePrizeList[target] = data;
    const list = formData.ladderPrizeList.map((item) => item.prizeKey);
    setPlan(list);
    setData(formData);
    setMultipleVisible(false);
  };
  const editMultipleRow = async (index) => {
    let row = formData.multiplePrizeList[index];
    if (row.prizeName === '') {
      row = null;
    } else if (getParams('type') === 'edit') {
      try {
        const data = await getPrizeRemain({
          prizeType: row.prizeType,
          activityId: getParams('id'),
          prizeKey: row.prizeKey,
        });
        const keyMap = {
          1: 'quantityRemain',
          2: 'quantityRemain',
          3: 'quantityAvailable',
          6: 'quantityRemain',
          7: 'cardSurplus',
          8: 'quantityRemain',
          9: 'quantityRemain',
          10: 'quantityRemain',
          12: 'quantityRemain',
        };
        row[keyMap[row.prizeType]] = +data >= 0 ? +data : 0;
      } catch (error) {
        console.error(error);
      }
    }
    setEditValue(row);
    setTarget(index);
    setMultipleVisible(true);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const setPrizes = () => {};
  useEffect((): void => {
    // 初始奖品列表长度
    // const ladderPrizeListLength = formData.ladderPrizeList.length;
    // const registrationPrizeListLength = formData.registrationPrizeList.length;
    const multiplePrizeListLength = formData.multiplePrizeList.length;
    // 生成默认奖品列表
    const list1: PrizeInfo[] = [...formData.ladderPrizeList];
    const list2: PrizeInfo[] = [...formData.registrationPrizeList];
    const list3: PrizeInfo[] = [...formData.multiplePrizeList];
    // if (!ladderPrizeListLength) {
    //   list1.push(PRIZE_INFO);
    // }
    // if (!registrationPrizeListLength) {
    //   list2.push(PRIZE_INFO);
    // }
    if (!multiplePrizeListLength) {
      for (let i = 0; i < 3; i++) {
        list3.push(PRIZE_INFO);
      }
    }
    setData({
      // ladderPrizeList: list1.length ? list1 : formData.ladderPrizeList,
      // registrationPrizeList: list2.length ? list2 : formData.registrationPrizeList,
      multiplePrizeList: list3.length ? list3 : formData.multiplePrizeList,
    });
  }, []);

  // const changeBuyTimes = (buyTimes, index) => {
  //   formData.ladderPrizeList[index].buyTimes = buyTimes;
  //   setData(formData);
  // };

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onLadderCancel = (): void => {
    setLadderVisible(false);
  };
  const onCancel = (): void => {
    setVisible(false);
  };
  const onMultipleCancel = (): void => {
    setMultipleVisible(false);
  };

  const addMultipleListPrize = () => {
    formData.multiplePrizeList.push({
      ...PRIZE_INFO,
      rank: '',
      id: `id-${Math.random().toString(36).substr(2, 9)}`,
      sortId: formData.multiplePrizeList.length,
    });
    setData(formData);
  };

  // const addLadderPrizeListPrize = () => {
  //   formData.ladderPrizeList.push({
  //     ...PRIZE_INFO,
  //     rank: '',
  //     id: `id-${Math.random().toString(36).substr(2, 9)}`,
  //     sortId: formData.ladderPrizeList.length,
  //     buyTimes: 1,
  //   });
  //   setData(formData);
  // };

  // // 删除阶梯当前行
  // const deleteLadderPrizeListRow = (index) => {
  //   const newPlan = plan.filter((item) => item !== formData.ladderPrizeList[index].prizeKey);
  //   setPlan(newPlan);
  //   formData.ladderPrizeList.splice(index, 1);
  //   setData(formData);
  // };

  // 删除多选奖励列表当前行
  const deleteMultiplePrizeListRow = (index) => {
    const newPlan = plan.filter((item) => item !== formData.multiplePrizeList[index].prizeKey);
    setPlan(newPlan);
    formData.multiplePrizeList.splice(index, 1);
    setData(formData);
  };

  const setExchangeImg = (index, data) => {
    formData.multiplePrizeList[index].exchangeImg = data;
    setData(formData);
  };

  const handleSkuChange = (data) => {
    setData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    formData.orderSkuList.splice(index, 1);
    setData({ orderSkuList: formData.orderSkuList });
  };

  const handlePreview = (data) => {
    setData({ orderSkuListPreview: data });
  };

  useEffect(() => {
    setMaxMultipleReceiveNum(formData.multiplePrizeList.length);
  }, [formData.multiplePrizeList.length]);

  return (
    <div>
      <LzPanel title="奖项设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          {/* <FormItem label="报名奖励列表" required> */}
          {/*  <Table dataSource={formData.registrationPrizeList}> */}
          {/*    <Table.Column title="奖项" width={80} cell={(_, index) => <div>{index + 1}</div>} /> */}
          {/*    <Table.Column title="奖项名称" width={200} dataIndex="prizeName" /> */}
          {/*    <Table.Column */}
          {/*      title="奖项类型" */}
          {/*      cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>} */}
          {/*      dataIndex="prizeType" */}
          {/*    /> */}
          {/*    <Table.Column */}
          {/*      title="单位数量" */}
          {/*      cell={(_, index, row) => { */}
          {/*        if (row.prizeType === 1) { */}
          {/*          return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>; */}
          {/*        } else { */}
          {/*          return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>; */}
          {/*        } */}
          {/*      }} */}
          {/*    /> */}
          {/*    <Table.Column */}
          {/*      title="发放份数" */}
          {/*      cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>} */}
          {/*    /> */}
          {/*    <Table.Column */}
          {/*      title="单份价值(元)" */}
          {/*      cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>} */}
          {/*    /> */}
          {/*    <Table.Column */}
          {/*      title="奖品图" */}
          {/*      cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />} */}
          {/*    /> */}
          {/*    {!activityEditDisabled() && ( */}
          {/*      <Table.Column */}
          {/*        title="操作" */}
          {/*        width={130} */}
          {/*        cell={(val, index, _) => ( */}
          {/*          <FormItem disabled={isDisableSetPrize(formData.registrationPrizeList, index)}> */}
          {/*            <Button */}
          {/*              text */}
          {/*              type="primary" */}
          {/*              onClick={() => { */}
          {/*                let row = formData.registrationPrizeList[index]; */}
          {/*                if (row.prizeName === '') { */}
          {/*                  row = null; */}
          {/*                } */}
          {/*                setEditValue(row); */}
          {/*                setTarget(index); */}
          {/*                setVisible(true); */}
          {/*              }} */}
          {/*            > */}
          {/*              <i className={`iconfont icon-chaojiyingxiaoicon-25`} /> */}
          {/*            </Button> */}
          {/*            <Button */}
          {/*              text */}
          {/*              type="primary" */}
          {/*              onClick={() => { */}
          {/*                if (_.prizeType) { */}
          {/*                  Dialog.confirm({ */}
          {/*                    v2: true, */}
          {/*                    title: '提示', */}
          {/*                    centered: true, */}
          {/*                    content: '确认清空该奖品？', */}
          {/*                    onOk: () => { */}
          {/*                      formData.registrationPrizeList = [PRIZE_INFO]; */}
          {/*                      setData(formData); */}
          {/*                    }, */}
          {/*                    onCancel: () => console.log('cancel'), */}
          {/*                  } as any); */}
          {/*                } */}
          {/*              }} */}
          {/*            > */}
          {/*              <i className={`iconfont icon-shanchu`} /> */}
          {/*            </Button> */}
          {/*            {formData.registrationPrizeList.length > 0 && index > 0 && ( */}
          {/*              <Button */}
          {/*                text */}
          {/*                type="primary" */}
          {/*                onClick={() => { */}
          {/*                  formData.registrationPrizeList.splice( */}
          {/*                    index - 1, */}
          {/*                    1, */}
          {/*                    ...formData.registrationPrizeList.splice( */}
          {/*                      index, */}
          {/*                      1, */}
          {/*                      formData.registrationPrizeList[index - 1], */}
          {/*                    ), */}
          {/*                  ); */}
          {/*                  setData(formData); */}
          {/*                }} */}
          {/*              > */}
          {/*                <i className={`iconfont icon-iconjiantou-35`} /> */}
          {/*              </Button> */}
          {/*            )} */}
          {/*            {formData.registrationPrizeList.length > 0 && */}
          {/*              index < formData.registrationPrizeList.length - 1 && ( */}
          {/*                <Button */}
          {/*                  text */}
          {/*                  type="primary" */}
          {/*                  onClick={() => { */}
          {/*                    formData.registrationPrizeList.splice( */}
          {/*                      index, */}
          {/*                      1, */}
          {/*                      ...formData.registrationPrizeList.splice( */}
          {/*                        index + 1, */}
          {/*                        1, */}
          {/*                        formData.registrationPrizeList[index], */}
          {/*                      ), */}
          {/*                    ); */}
          {/*                    setData(formData); */}
          {/*                  }} */}
          {/*                > */}
          {/*                  <i className={`iconfont icon-iconjiantou-34`} /> */}
          {/*                </Button> */}
          {/*              )} */}
          {/*          </FormItem> */}
          {/*        )} */}
          {/*      /> */}
          {/*    )} */}
          {/*  </Table> */}
          {/* </FormItem> */}
          <FormItem label="复购多选奖励列表" required>
            <Table dataSource={formData.multiplePrizeList}>
              <Table.Column title="奖项" width={80} cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="兑换流程图"
                cell={(_, index, row) =>
                  row.prizeType === 7 ? (
                    <>
                      <Form.Item disabled={false} style={{ marginRight: 10, marginBottom: 0 }}>
                        <LzImageSelector
                          disabled={false}
                          value={row.exchangeImg}
                          onChange={(exchangeImg: string) => {
                            setExchangeImg(index, exchangeImg);
                          }}
                        />
                      </Form.Item>
                    </>
                  ) : (
                    <div>--</div>
                  )
                }
              />

              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem disabled={isDisableSetPrize(formData.multiplePrizeList, index)}>
                    <Button
                      text
                      type="primary"
                      onClick={() => {
                        editMultipleRow(index);
                      }}
                    >
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {!activityEditDisabled() && formData.multiplePrizeList.length > 3 && (
                      <Button text type="primary" onClick={() => deleteMultiplePrizeListRow(index)}>
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                    )}
                  </FormItem>
                )}
              />
            </Table>
            <Button
              type={'primary'}
              disabled={activityEditDisabled() || formData.multiplePrizeList.length >= 16}
              onClick={addMultipleListPrize}
              style={{ marginTop: 10 }}
            >
              添加奖项({formData.multiplePrizeList.length}/16)
            </Button>
          </FormItem>
          <FormItem label="复购多选奖励列表最大领取份数" required requiredMessage="请输入复购多选奖励列表最大领取份数">
            <NumberPicker
              name="receiveNum"
              min={1}
              max={maxMultipleReceiveNum}
              type="inline"
              value={formData.receiveNum}
              onChange={(receiveNum: number) => setData({ receiveNum })}
            />{' '}
            份
          </FormItem>
          {/* <FormItem label="订单商品" required> */}
          {/*  <RadioGroup */}
          {/*    value={formData.orderSkuisExposure} */}
          {/*    onChange={(orderSkuisExposure: number) => { */}
          {/*      setData({ orderSkuisExposure, orderSkuList: [], orderSkuListPreview: [] }); */}
          {/*    }} */}
          {/*  > */}
          {/*    <Radio id="0" value={0}> */}
          {/*      全部商品 */}
          {/*    </Radio> */}
          {/*    <Radio id="1" value={1}> */}
          {/*      指定商品 */}
          {/*    </Radio> */}
          {/*    /!* <Radio id="2" value={2}> *!/ */}
          {/*    /!*  排除商品 *!/ */}
          {/*    /!* </Radio> *!/ */}
          {/*  </RadioGroup> */}
          {/*  <Grid.Row> */}
          {/*    {(formData.orderSkuisExposure === 1 || formData.orderSkuisExposure === 2) && ( */}
          {/*      <FormItem */}
          {/*        name="orderSkuList" */}
          {/*        required */}
          {/*        requiredMessage={'请选择订单商品'} */}
          {/*        style={{ marginTop: '15px' }} */}
          {/*      > */}
          {/*        <Input */}
          {/*          className="validateInput" */}
          {/*          name="orderSkuList" */}
          {/*          value={formData.orderSkuList.length ? 1 : ''} */}
          {/*        /> */}
          {/*        <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} /> */}
          {/*        <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={removeSku} /> */}
          {/*        <p className={styles.tip}>注：商品价格每天凌晨同步;</p> */}
          {/*      </FormItem> */}
          {/*    )} */}
          {/*  </Grid.Row> */}
          {/* </FormItem> */}
        </Form>
      </LzPanel>
      {/* <LzPanel title="阶梯奖项设置"> */}
      {/*  <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}> */}
      {/*    <FormItem label="阶梯奖励列表" required> */}
      {/*      <Table dataSource={formData.ladderPrizeList}> */}
      {/*        <Table.Column title="奖项" width={80} cell={(_, index) => <div>{index + 1}</div>} /> */}
      {/*        <Table.Column */}
      {/*          title="购买次数" */}
      {/*          cell={(_, index, row) => ( */}
      {/*            <div> */}
      {/*              <NumberPicker */}
      {/*                disabled={activityEditDisabled()} */}
      {/*                name="buyTimes" */}
      {/*                min={1} */}
      {/*                max={10} */}
      {/*                type="inline" */}
      {/*                defaultValue={1} */}
      {/*                value={row.buyTimes} */}
      {/*                onChange={(buyTimes) => changeBuyTimes(buyTimes, index)} */}
      {/*              />{' '} */}
      {/*              次 */}
      {/*            </div> */}
      {/*          )} */}
      {/*        /> */}
      {/*        <Table.Column title="奖项名称" width={200} dataIndex="prizeName" /> */}
      {/*        <Table.Column */}
      {/*          title="奖项类型" */}
      {/*          cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>} */}
      {/*          dataIndex="prizeType" */}
      {/*        /> */}
      {/*        <Table.Column */}
      {/*          title="单位数量" */}
      {/*          cell={(_, index, row) => { */}
      {/*            if (row.prizeType === 1) { */}
      {/*              return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>; */}
      {/*            } else { */}
      {/*              return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>; */}
      {/*            } */}
      {/*          }} */}
      {/*        /> */}
      {/*        <Table.Column */}
      {/*          title="发放份数" */}
      {/*          cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>} */}
      {/*        /> */}
      {/*        <Table.Column */}
      {/*          title="单份价值(元)" */}
      {/*          cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>} */}
      {/*        /> */}
      {/*        <Table.Column */}
      {/*          title="奖品图" */}
      {/*          cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />} */}
      {/*        /> */}
      {/*        {!activityEditDisabled() && ( */}
      {/*          <Table.Column */}
      {/*            title="操作" */}
      {/*            width={130} */}
      {/*            cell={(val, index, _) => ( */}
      {/*              <FormItem disabled={isDisableSetPrize(formData.ladderPrizeList, index)}> */}
      {/*                <Button */}
      {/*                  text */}
      {/*                  type="primary" */}
      {/*                  onClick={() => { */}
      {/*                    let row = formData.ladderPrizeList[index]; */}
      {/*                    if (row.prizeName === '') { */}
      {/*                      row = null; */}
      {/*                    } */}
      {/*                    setEditValue(row); */}
      {/*                    setTarget(index); */}
      {/*                    setLadderVisible(true); */}
      {/*                  }} */}
      {/*                > */}
      {/*                  <i className={`iconfont icon-chaojiyingxiaoicon-25`} /> */}
      {/*                </Button> */}
      {/*                <Button text type="primary" onClick={() => deleteLadderPrizeListRow(index)}> */}
      {/*                  <i className={`iconfont icon-shanchu`} /> */}
      {/*                </Button> */}
      {/*              </FormItem> */}
      {/*            )} */}
      {/*          /> */}
      {/*        )} */}
      {/*      </Table> */}
      {/*      <Button */}
      {/*        type={'primary'} */}
      {/*        disabled={activityEditDisabled() || formData.ladderPrizeList.length >= 10} */}
      {/*        onClick={addLadderPrizeListPrize} */}
      {/*        style={{ marginTop: 10 }} */}
      {/*      > */}
      {/*        添加奖项({formData.ladderPrizeList.length}/10) */}
      {/*      </Button> */}
      {/*    </FormItem> */}
      {/*  </Form> */}
      {/* </LzPanel> */}

      {/* <LzDialog */}
      {/*  title={false} */}
      {/*  visible={ladderVisible} */}
      {/*  footer={false} */}
      {/*  onClose={() => setLadderVisible(false)} */}
      {/*  style={{ width: '670px' }} */}
      {/* > */}
      {/*  <YiLiChoosePrize */}
      {/*    formData={formData} */}
      {/*    editValue={editValue} */}
      {/*    onChange={onLadderPrizeChange} */}
      {/*    typeList={[2, 4]} */}
      {/*    defaultTarget={2} */}
      {/*    onCancel={onLadderCancel} */}
      {/*    hasLimit={false} */}
      {/*    hasProbability={false} */}
      {/*  /> */}
      {/* </LzDialog> */}

      {/* <LzDialog */}
      {/*  title={false} */}
      {/*  visible={visible} */}
      {/*  footer={false} */}
      {/*  onClose={() => setVisible(false)} */}
      {/*  style={{ width: '670px' }} */}
      {/* > */}
      {/*  <YiLiChoosePrize */}
      {/*    formData={formData} */}
      {/*    editValue={editValue} */}
      {/*    onChange={onPrizeChange} */}
      {/*    typeList={[2, 4]} */}
      {/*    defaultTarget={2} */}
      {/*    onCancel={onCancel} */}
      {/*    hasLimit={false} */}
      {/*    hasProbability={false} */}
      {/*  /> */}
      {/* </LzDialog> */}

      <LzDialog
        title={false}
        visible={multipleVisible}
        footer={false}
        onClose={() => setMultipleVisible(false)}
        style={{ width: '670px' }}
      >
        <YiLiChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onMultiplePrizeChange}
          typeList={[2, 3, 7]}
          defaultTarget={7}
          onCancel={onMultipleCancel}
          hasLimit={false}
          hasProbability={false}
        />
      </LzDialog>
    </div>
  );
};
