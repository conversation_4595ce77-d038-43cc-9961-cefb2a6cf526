import LzPanel from '@/components/LzPanel';
import { formItemLayout } from '@/pages/mengniu/public/scripts/commonSetting';
import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import constant from '@/utils/constant';
import { Card, DatePicker2, Form } from '@alifd/next';
import dayjs from 'dayjs';
import React from 'react';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

export default function BaseInfo({ data, resetData, updateCurrentModuleData, field, state }) {
  const { showStartTime, showEndTime } = data;

  const validateDeliveryTime = (rule, values, callback) => {
    if (!values || !values.length || values.filter((item) => !item).length > 0) {
      return callback('请选择投放时间');
    }
    const [start, end] = values;
    if (start === end) {
      return callback('投放时间不能一致');
    }

    return callback();
  };

  return (
    <Card free className={decorateStyles['card-container']}>
      <Card.Header className={decorateStyles['card-header']} title={<span className="crm-label">活动基本信息</span>} />
      <Card.Content>
        <LzPanel>
          <Form field={field} {...formItemLayout}>
            <Form.Item
              name="deliveryTime"
              label="投放时间"
              required
              requiredMessage="请选择投放时间"
              validator={validateDeliveryTime}
              extra={<div className="next-form-item-extra">模块在C端显示的时间</div>}
            >
              <DatePicker2.RangePicker
                className="w-300"
                format={dateFormat}
                outputFormat={dateFormat}
                timePanelProps={{ defaultValue: '00:00:00' }}
                hasClear={false}
                showTime
                value={[showStartTime, showEndTime]}
                disabledDate={(value, mode) => {
                  return value.isBefore(dayjs().subtract(1, 'day'));
                }}
                onChange={(value) => {
                  updateCurrentModuleData({
                    showStartTime: value[0],
                    showEndTime: value[1],
                  });
                }}
              />
            </Form.Item>
          </Form>
        </LzPanel>
      </Card.Content>
    </Card>
  );
}
