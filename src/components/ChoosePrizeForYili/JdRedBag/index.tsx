import { Radio, Form, Button, NumberPicker, Input, Dialog, Grid, Field, Message } from '@alifd/next';
import React, { useState, useReducer, useEffect } from 'react';
import Plan from './components/Plan';
import active from '../assets/active.png';
import notActive from '../assets/not-active.png';
import delIcon from '../assets/del-icon.png';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { activityEditDisabled, numRegularCheckInt } from '@/utils';
import format from '@/utils/format';
import { prizeFormLayout } from '@/components/ChoosePrize';

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

interface ComponentProps {
  [propName: string]: any;
}

const PropertyRedBag = ({
  editValue,
  onChange,
  onCancel,
  hasLimit = true,
  hasProbability = true,
  width,
  height,
  prizeNameLength,
  formData,
  sendTotalCountMax = 999999999,
}: ComponentProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/214055/33/4729/14449/61946651E1ce8e563/d568024bdefb6ffa.png';
  const planImg = require('../assets/5.jpg');
  const defaultValue = {
    prizeKey: null,
    prizeType: 6,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): void | boolean => {
    if (prizeData.sendTotalCount > prizeData.quantityRemain) {
      Message.error(`发放份数不能大于奖品库存${prizeData.quantityRemain}`);
      return false;
    }
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    // if (hasProbability && +prizeData.probability <= 0) {
    //   Message.error(`中奖概率必须大于0`);
    //   return false;
    // }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100%`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    data.prizeName = '';
    data.unitPrice = '';
    setPrizeData(data);
  };
  const onSubmit = (resource: any) => {
    if (!resource) {
      return;
    }
    resource.prizeKey = resource.planId;
    resource.prizeName = resource.planName.substring(0, prizeNameLength);
    resource.unitPrice = (resource.aveAmount / 100).toFixed(2);
    setPrizeData(resource);
    setWinJdShow(false);
    field.setErrors({ prizeKey: '', prizeName: '' });
  };
  return (
    <div className={styles.PropertyRedBag}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item style={{ paddingTop: '15px' }} required requiredMessage="请选择红包">
          <Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={prizeData.planStatus === 2 ? styles.beanPrizeBg : styles.beanPrizeBgy}>
              <img className={styles.prizeBg} src={prizeData.planStatus === 2 ? active : notActive} alt="" />
              {!activityEditDisabled() && (
                <div
                  onClick={() => delPlan()}
                  style={{ backgroundImage: `url(${delIcon})` }}
                  className={styles.delIcon}
                />
              )}
              <div style={{ display: 'flex' }}>
                <div style={{ width: 230 }} className={styles.preview}>
                  <Form labelAlign="left" {...formItemLayout}>
                    <Form.Item label="计划名称：">
                      <div className={styles.ellipsis}>{prizeData.planName}</div>
                    </Form.Item>
                    <Form.Item label="计划ID：">
                      <div className={styles.ellipsis}>{prizeData.planId}</div>
                    </Form.Item>
                    <Form.Item label="创建时间：">
                      <div>{format.formatDateTimeDayjs(prizeData.createTime)}</div>
                    </Form.Item>

                    <Form.Item label="计划时间：">
                      <div>
                        <div> 起：{format.formatDateTimeDayjs(prizeData.startDate)}</div>
                        <div> 止：{format.formatDateTimeDayjs(prizeData.endDate)}</div>
                      </div>
                    </Form.Item>
                  </Form>
                </div>
                <div style={{ width: 260 }} className={styles.preview}>
                  <Form labelAlign="left" {...formItemLayout}>
                    <Form.Item label="红包有效期：">
                      {prizeData.hongBaoExpireType === 1 && (
                        <div>
                          <div>起： {format.formatDateTimeDayjs(prizeData.packetDataTime[0])}</div>
                          <div>止： {format.formatDateTimeDayjs(prizeData.packetDataTime[1])}</div>
                        </div>
                      )}
                      {prizeData.hongBaoExpireType === 2 && (
                        <div>
                          红包领取后&nbsp;
                          {prizeData.hongBaoExpireDay}
                          &nbsp; 天内使用，逾期失效
                        </div>
                      )}
                    </Form.Item>
                    <Form.Item label="领取限制：">
                      <div>
                        每人每天限领{prizeData.userDayCount}张 每人计划期间限领{prizeData.userActivityCount}张
                      </div>
                    </Form.Item>
                    <Form.Item label="单份红包金额：">
                      <div>{(prizeData.aveAmount / 100).toFixed(2)}元</div>
                    </Form.Item>
                    <Form.Item label="计划描述：">
                      <div className={styles.ellipsis}>{prizeData.planContent || '-'}</div>
                    </Form.Item>
                    <Form.Item label="剩余数量：">
                      <div>{prizeData.quantityRemain}份</div>
                    </Form.Item>
                  </Form>
                </div>
              </div>
            </div>
          )}
        </Form.Item>

        <Form.Item label="单份价值" disabled>
          <NumberPicker
            className={styles.formNumberPicker}
            onChange={(unitPrice: any) => setData({ unitPrice })}
            name="unitPrice"
            type="inline"
            precision={2}
            max={9999999}
            min={0.01}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        <Form.Item
          label="发放份数"
          required
          requiredMessage="请输入发放份数"
          validator={numRegularCheckInt}
          extra={<div style={{ color: 'red' }}>注：发放份数指本次活动计划投入的红包份数</div>}
        >
          <NumberPicker
            placeholder="请输入发放份数"
            className={styles.formNumberPicker}
            type="inline"
            min={1}
            max={sendTotalCountMax}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount, unitCount: 1 })}
            disabled={activityEditDisabled()}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              placeholder="请输入中奖概率"
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />

            <div style={{ marginTop: 5 }}>中奖概率不建议为0%，易引发客诉，请慎重</div>
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item
            label="每日限额"
            required
            requiredMessage="请输入每日限额"
            extra={<div>注：指商家每日最多发放多少份奖品，不是一个用户每日最多可以领取多少次奖品</div>}
          >
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType}
                onChange={(dayLimitType) => setData({ dayLimitType, dayLimit: dayLimitType === 1 ? 0 : '' })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    placeholder="请输入每日限额"
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择红包"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} />
      </Dialog>
    </div>
  );
};

export default PropertyRedBag;
