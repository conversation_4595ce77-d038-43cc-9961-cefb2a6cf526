import LzColorPicker from '@/components/LzColorPicker';
import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import { Box, Button } from '@alifd/next';
import React from 'react';

export default function ColorSetting({ value, onChange, onReset }) {
  return (
    <Box direction="row" align="center">
      <LzColorPicker value={value} onChange={onChange} />
      <Button text type="primary" className={decorateStyles['reset-button']} onClick={onReset}>
        重置
      </Button>
    </Box>
  );
}
