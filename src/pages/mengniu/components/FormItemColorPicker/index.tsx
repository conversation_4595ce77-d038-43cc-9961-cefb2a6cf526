import LzColorPicker from '@/components/LzColorPicker';
import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import { Box, Button } from '@alifd/next';
import React from 'react';

export default function FormItemColorPicker({ valueKey, data, updateCurrentModuleData, resetData }) {
  const { jsonData } = data;
  return (
    <Box direction="row" align="center">
      <LzColorPicker
        value={jsonData[valueKey]}
        onChange={(color) => {
          updateCurrentModuleData({
            jsonData: {
              ...jsonData,
              [valueKey]: color,
            },
          });
        }}
      />
      <Button
        text
        type="primary"
        className={decorateStyles['reset-button']}
        onClick={() => {
          updateCurrentModuleData({
            jsonData: {
              ...jsonData,
              [valueKey]: resetData.jsonData[valueKey],
            },
          });
        }}
      >
        重置
      </Button>
    </Box>
  );
}
