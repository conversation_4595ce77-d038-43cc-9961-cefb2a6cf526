import { lazy } from 'ice';

import { activities, previewActivities } from '@/routers/activity';

import commonReportData from './common.router';
import apple from './apple.router';
import aptamil from './aptamil.router';
import decorate from './decorate.router';
import feihe from './feihe.router';
import feihePop from './feihePop.router';
import huggies from './huggies.router';
import langjiu from './langjiu.router';
import mengniu from './mengniu.router';
import proya from './proya.router';
import smartCardRouter from './smartCard.routers';
import yili from './yili.router';
import heshengyuan from '@/routers/heshengyuan.router';
import mengniuNiudan from './mengniuNiudan.router';
import swisse from './swisse.router';
import sanyuan from './sanyuan.router';
import legendSandy from './legendSandy.router';
import vivo from './vivo.router';
import pg from './pg.router';
import pechoin from './pechoin.router';
import babyCare from './babyCare.router';
import oppo from './oppo.router';
import lamer from './lamer.router';
import kabrita from './kabrita.router';
import nestle from './nestle.router';
import caltrate from './caltrate.router';
import customized from './customized.router';
import propertyShoppingCard from './property.router';

const BosidengSelect = lazy(() => import('@/pages/bosideng/select'));
const BosidengSquqre = lazy(() => import('@/pages/bosideng/square'));
const ActivityBosideng = lazy(() => import('@/pages/bosideng/list'));

const Home = lazy(() => import('@/pages/home'));
const ActivityList = lazy(() => import('@/pages/core/list'));
const ActivitySelect = lazy(() => import('@/pages/core/select'));
const Draggable = lazy(() => import('@/pages/core/draggable'));
const CustomizedManage = lazy(() => import('@/pages/customizedManage'));
const ActivitySquare = lazy(() => import('@/pages/core/square'));

export const routers = [
  ...propertyShoppingCard,
  ...nestle,
  ...caltrate,
  ...aptamil,
  {
    name: '首页',
    path: '/',
    exact: true,
    component: Home,
  },
  {
    name: '活动列表',
    path: '/activityManage',
    exact: true,
    component: ActivityList,
  },
  {
    name: '互动广场',
    path: '/interactiveSquare',
    exact: true,
    component: ActivitySquare,
  },
  {
    path: '/activity/select',
    exact: true,
    component: ActivitySelect,
  },
  {
    path: '/draggable',
    exact: true,
    component: Draggable,
  },
  {
    path: '/customizedManage',
    exact: true,
    name: '交付中心',
    component: CustomizedManage,
  },
  {
    name: '波司登互动广场',
    path: '/bosideng/square',
    exact: true,
    component: BosidengSquqre,
  },
  {
    name: '波司登活动列表',
    path: '/bosideng/activity',
    exact: true,
    component: ActivityBosideng,
  },
  {
    path: '/bosideng/select',
    exact: true,
    component: BosidengSelect,
  },
  ...activities,
  ...previewActivities,
  ...commonReportData,
  ...heshengyuan,
  ...pg,
  ...activities,
  ...previewActivities,
  ...smartCardRouter,
  ...customized,
  ...decorate,
  ...feihe,
  ...langjiu,
  ...proya,
  ...mengniu,
  ...huggies,
  ...yili,
  ...feihePop,
  ...apple,
  ...mengniuNiudan,
  ...swisse,
  ...sanyuan,
  ...legendSandy,
  ...pechoin,
  ...babyCare,
  ...vivo,
  ...oppo,
  ...lamer,
  ...kabrita,
];

export default routers;
