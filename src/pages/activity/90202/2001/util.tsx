/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  potNum: string;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  prizeKey: string;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface seriesSkuList {
  skuId: string;
  tankNum: number;
}

export interface SeriesList {
  seriesName: string;
  seriesSkuList: seriesSkuList[];
  totalReceiveCount: number | string;
  perReceiveCount: number | string;
  prizeReceiveLimit: number;
  stepList: StepList[];
  previewSkuList: any[];
}

export interface StepList {
  stepName: string;
  tankNum: string;
  prizeList: PrizeInfo[];
}

export interface CustomValue {
  pageBg: string;
  actBg: string;
  actBgColor: string;
  ruleBtn: string;
  myPrizeBtn: string;
  orderBtn: string;
  userInfoBg: string;
  userInfoColor: string;
  // seriesBg: string;
  // seriesTitleColor: string;
  // bugAmountColor: string;
  thresholdBg: string;
  stepBtnBg: string;
  stepBtnSelectBg: string;
  stepTextColor: string;
  stepTextColorShadow: string;
  prizeBg: string;
  prizeBorder: string;
  exchangeBtn: string;
  skuListBg: string;
  skuBg: string;
  skuTitleBg: string;
  skuTitleColor: string;
  goSkuBtn: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  prizeDay: PrizeInfo[];
  limitOrder: number;
  orderStartTime: string;
  orderEndTime: string;
  actLimitActTotalReceiveCount: number;
  actTotalReceiveCount: number | string;
  seriesList: SeriesList[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/263717/2/22059/624502/67b6c866Feef36d18/2f88ecf2580c4a3b.png',
  actBg: '', // 主页背景图
  actBgColor: '#e8e3da', // 主页背景色
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/228105/27/33270/2446/67b6c86aFfd65e0a7/3cb2aed142c88a15.png', // 活动规则按钮
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/259477/23/22154/2504/67b6c86bFe2fb5b0e/e0dd5dd9ed8969ad.png', // 我的奖品按钮
  orderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/258068/14/22310/2339/67b6c867F71ff5bb6/8f9f40158a7be60f.png', // 我的订单按钮
  userInfoBg: '//img10.360buyimg.com/imgzone/jfs/t1/252918/37/22664/42952/67b6c869F855546ad/54e323eb5bf6c8d2.png', // 用户信息背景图
  userInfoColor: '#97653c', // 用户信息字体颜色
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/263056/17/22232/95667/67b6c86bF8e156c3b/05b13ed2f6e4eeac.png', // 门槛背景图
  // seriesBg: '', // 系列背景图
  // seriesTitleColor: '', // 系列标题颜色
  // bugAmountColor: '', // 购买金额颜色
  stepBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/254825/4/23220/4784/67b6c869F8eefa3fe/c2f4752ac43fb454.png', // 门槛按钮
  stepBtnSelectBg: '//img10.360buyimg.com/imgzone/jfs/t1/254825/4/23220/4784/67b6c869F8eefa3fe/c2f4752ac43fb454.png', // 门槛按钮选中
  stepTextColor: '#e0362a', // 阶梯文字颜色
  stepTextColorShadow: '#fff', // 阶梯文字阴影颜色
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/259850/19/21830/135972/67b6c86bFe24ddd6d/73efdd3021a0e2a8.png', // 奖品背景图
  prizeBorder: '//img10.360buyimg.com/imgzone/jfs/t1/260947/26/22002/3870/67b6c869F9d2e191b/5bd1dd8e75f0a2e7.png',
  exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/264505/37/22187/4140/67b6c869F1a12ff97/997d4d0411856a73.png', // 兑换按钮
  skuListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/80196/10/27770/128910/66dff8c1Fd8dd7d8f/666f84f37a8a83cf.png', // 商品列表背景图
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/97264/18/48609/13072/66e12e18Fd2700ee4/5cc9c135ed83bb7d.png', // 商品背景图
  skuTitleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/243580/22/16339/2665/66dff8c2Fe83e05c4/8f5a41ccc7212189.png', // 商品标题背景图
  skuTitleColor: '#f8f1e0', // 商品标题颜色
  goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/156269/19/45386/5947/66dff8c3F6a3f18a8/dc58e86af778aa79.png', // 去商品按钮
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  const now = (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00');
  const after30 = (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59');
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `满罐阶梯礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [now, after30],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(now),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(after30),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    limitOrder: 1,
    orderStartTime: format.formatDateTimeDayjs(now),
    orderEndTime: format.formatDateTimeDayjs(after30),
    // 每日签到奖品
    prizeDay: [],
    // 是否限制活动内总兑换次数 0 不限制 1 限制
    actLimitActTotalReceiveCount: 0,
    // 活动内总兑换次数
    actTotalReceiveCount: '',
    // 系列及奖品列表
    seriesList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '满罐赢好礼，超多惊喜大奖等你来领！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    // 模板code
    templateCode: '2001',
    // 会员等级标签
    gradeLabel: [],
    // 人群包
    crowdBag: '',
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  potNum: '',
  ifPlan: 0,
  prizeKey: '',
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 领奖开始时间校验
const checkAwardStartTime = (awardStartTime, formData) => {
  // const isBefore: boolean = dayjs(awardStartTime).isBefore(formData.startTime);
  if (dayjs(awardStartTime).valueOf() < dayjs(formData.rangeDate[0].valueOf()).valueOf()) {
    Message.error('领奖开始时间需大于等于活动开始时间');
    return false;
  }
  return true;
};
// 领奖结束时间校验After
const checkAwardEndTime = (awardEndTime, formData) => {
  console.log('领奖结束时间', dayjs(awardEndTime).valueOf());
  console.log('活动结束时间', dayjs(formData.endTime).valueOf());
  console.log('活动结束时间数组', dayjs(formData.rangeDate[1]).valueOf());

  // const isAfter: boolean = dayjs(awardEndTime).isBefore(awardEndTime);
  if (dayjs(awardEndTime).valueOf() > dayjs(formData.rangeDate[1].valueOf()).valueOf()) {
    Message.error('领奖结束时间需小于等于活动结束时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${prize.prizeName}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${prize.prizeName}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};

// 校验系列及活动商品为必填项
export const isSeriesValid = (formData: PageData): boolean => {
  if (!formData.seriesList || formData.seriesList.length === 0) {
    Message.error('请上传系列数据');
    return false;
  }
  return true;
};

const hasPrizeDay = (formData: PageData): boolean => {
  for (const item of formData.prizeDay) {
    if (!item.prizeName) {
      Message.error('请设置每日签到奖励');
      return false;
    }
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {
  if (!formData.seriesList.length) {
    Message.error('请上传系列数据');
    return false;
  }
  for (const series of formData.seriesList) {
    if (!series.stepList.length) {
      Message.error(`请在${series.seriesName}满罐礼下添加阶梯`);
      return false;
    }
    for (const step of series.stepList) {
      if (!step.prizeList.length) {
        Message.error(`请在${series.seriesName}满罐礼-${step.stepName}下添加奖品`);
        return false;
      }
      for (const prize of step.prizeList) {
        if (!prize.prizeName) {
          Message.error('请设置奖品');
          return false;
        }
        if (!isPrizeValid(prize, formData)) {
          return false;
        }
      }
    }
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }

  if (!hasPrize(formData)) {
    return false;
  }

  return true;
};
