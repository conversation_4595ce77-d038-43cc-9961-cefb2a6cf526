import React from 'react';
import { Radio, Checkbox, Divider, Button } from '@alifd/next';
import { getVenderLevelRule } from '@/api/common';
import styles from './index.module.scss';

const RadioGroup = Radio.Group;

export default ({ onClose, dispatch, dataIndex, dataList }) => {
  const [limit, setLimit] = React.useState<number>(dataList[dataIndex].limit);
  const [levels, setLevels] = React.useState<any[]>([]);
  const [selectedOptions, setSelectedOptions] = React.useState<number[]>(dataList[dataIndex].limitLevels); // 用于跟踪已选中的选项

  const getMemberLevels = async () => {
    const res = await getVenderLevelRule();
    setLevels(res);
  };

  const handleCheckboxChange = (selectedValues) => {
    setSelectedOptions(selectedValues);
  };

  const saveSetting = () => {
    const updatedDataList = [...dataList];
    updatedDataList[dataIndex] = {
      ...updatedDataList[dataIndex],
      ...{
        limit,
        limitLevels: selectedOptions,
      },
    };
    dispatch({ type: 'UPDATE_MODULE', payload: updatedDataList });
    console.log('updatedDataList', updatedDataList);
    onClose();
  };

  React.useEffect(() => {
    getMemberLevels().then();
  }, []);
  return (
    <div>
      <div className={styles.radio}>
        <div>会员等级：</div>
        <div>
          <RadioGroup
            value={limit}
            onChange={(val: number) => {
              setSelectedOptions([]);
              setLimit(val);
            }}
          >
            <Radio id="0" value={0}>
              不限制
            </Radio>
            <Radio id="1" value={1}>
              限制
            </Radio>
          </RadioGroup>
        </div>
      </div>
      {!!limit && (
        <div className={styles.checkbox}>
          <Checkbox.Group onChange={handleCheckboxChange} value={selectedOptions}>
            {levels.map((option) => (
              <Checkbox className={styles.item} key={option.customerLevel} value={option.customerLevel}>
                {option.customerLevelName}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </div>
      )}
      <Divider />
      <div className={styles.footer}>
        <Button type={'primary'} onClick={saveSetting}>
          确定
        </Button>
        <Button onClick={onClose}>取消</Button>
      </div>
    </div>
  );
};
