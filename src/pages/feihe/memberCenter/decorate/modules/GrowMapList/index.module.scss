$px-scale: 20;
.preview {
  width: 100%;
  position: relative;
  border-radius: 8.5px;
  overflow: hidden;
  .grow-map-container {
    box-sizing: border-box;
    height: 5.7px * $px-scale;
    margin-bottom: 0.3px * $px-scale;
    padding: 1.4px * $px-scale 0.45px * $px-scale 0 0.35px * $px-scale;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/236825/38/11163/16683/65af4ef7F55237fc7/2b1835fe050abcf6.png');
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100% auto;
    border-radius: 0.3px * $px-scale;
    .grow-map-list {
      overflow-x: scroll;
      display: flex;
    }
    // .grow-map-list::-webkit-scrollbar {
    //   display: none;
    // }

    .grow-map-item {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 25%;
      height: 60px;
      height: 3.4px * $px-scale;
    }

    .grow-map-item-icon {
      width: 1.85px * $px-scale;
      height: 1.85px * $px-scale;
      background-repeat: no-repeat;
      background-position: center bottom;
      background-size: 100% 100%;
    }

    .grow-map-item-name {
      color: #503622;
      font-size: 9px;
    }
  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  .tip {
  }
  .kvContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;

    .imgContainer {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .imgUpload {
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-right: 10px;
      }
      .colorPicker {
        display: flex;
        margin-top: 10px;
        gap: 20px;
      }
    }
  }
}
