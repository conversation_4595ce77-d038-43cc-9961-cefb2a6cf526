/**
 * 大转盘抽奖数据报表
 */
import React, { useEffect, useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import JoinRecord from './components/JoinRecord';
import LzDocGuide from '@/components/LzDocGuide';
import { getParams } from '@/utils';
// import { num } from '@/api/v21003';
// import { getParams } from '@/utils';
// import exportCombinedLogs from '@/utils/exportAll';

export default () => {
  // const [actNum, setActNum] = useState(12);

  // const getNum = async () => {
  //   try {
  //     const data = await num({ activityId: getParams('id') });
  //     setActNum(Number(data));
  //   } catch (e) {
  //     console.error(e);
  //   }
  // };

  useEffect(() => {
    // getNum();
  }, []);

  const [activeKey, setActiveKey] = useState('1');
  return (
    // {`${getParams('name') ? getParams('name') : '全渠道新客礼2.0'}活动报表`}
    <div className="crm-container">
      <LzPanel title={`${getParams('name') ? getParams('name') : '满额有礼'}活动记录`} actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="参与记录" key="1">
            <JoinRecord />
          </Tab.Item>
        
        </Tab>
      </LzPanel>
    </div>
  );
};
