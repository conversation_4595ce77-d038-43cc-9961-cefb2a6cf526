import CONST from '@/utils/constant';
import { Loading, Upload } from '@alifd/next';
import { config } from 'ice';
import React, { useState } from 'react';

export default (props) => {
  const { action, data, onSuccess, children } = props;

  const [loadingUpload, setLoadingUpload] = useState(false);

  const headers = {
    token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
    prd: 'crm',
  };

  const onProgress = ({ percent }) => {
    console.log('percent', percent);
  };

  const uploader = new Upload.Uploader({
    action: `${config.baseURL}${action}`,
    name: 'file',
    withCredentials: false,
    data,
    headers,
    onProgress,
    onSuccess: (res) => {
      onSuccess(res);
      setLoadingUpload(false);
    },
  });

  // 选择文件后
  const onSelect = async (files) => {
    const file = files[0];
    setLoadingUpload(true);
    uploader.startUpload(file);
  };

  return (
    <>
      <Upload.Selecter
        accept=".xlsx"
        onSelect={(files) => {
          onSelect(files);
        }}
      >
        <Loading visible={loadingUpload}>{children}</Loading>
      </Upload.Selecter>
    </>
  );
};
