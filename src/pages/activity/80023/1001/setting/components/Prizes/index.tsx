/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Checkbox, Radio, NumberPicker } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import { activityEditDisabled, deepCopy, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import styles from '../../style.module.scss';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  // 暂存累计/连续签到天数
  const [signDaySto, setSignDaySto] = useState('');

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 每日签到奖励
  const changeDayStatus = (val) => {
    if (val) {
      setData({ prizeDay: [deepCopy(PRIZE_INFO)], dayStatus: 1 });
    } else {
      setData({ prizeDay: [], dayStatus: 0 });
    }
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    if (data.prizeType === 1) {
      // 后端用于发奖和保存校验需要
      data.prizeKey = target.toString();
    }
    // 更新指定index 奖品信息
    if (tableName === 'prizeDay') {
      formData.prizeDay[target] = data;
    } else if (tableName === 'prizeList') {
      formData.prizeList[target] = { ...data, signDay: signDaySto };
    }
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.prizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.prizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 1 - prizeListLength; i++) {
      list.push(deepCopy(PRIZE_INFO));
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({ prizeList: list.length ? list : formData.prizeList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel title="签到奖品设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem required label="开启每日签到奖励">
            <FormItem>
              <Checkbox checked={!!formData.dayStatus} onChange={changeDayStatus} name="dayStatus">
                开启每日签到奖励
              </Checkbox>
            </FormItem>
            <div className={styles.tip}>开启后可设置每日签到奖励</div>
            {formData.dayStatus === 1 && (
              <FormItem>
                <Table dataSource={formData.prizeDay} style={{ marginTop: '15px' }}>
                  <Table.Column title="奖品名称" dataIndex="prizeName" />
                  <Table.Column
                    title="奖品类型"
                    cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                    dataIndex="prizeType"
                  />
                  <Table.Column
                    title="单位数量"
                    cell={(_, index, row) => {
                      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                    }}
                  />
                  <Table.Column
                    title="发放份数"
                    cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                  />
                  <Table.Column
                    title="单份价值(元)"
                    cell={(_, index, row) => (
                      <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                    )}
                  />
                  <Table.Column
                    title="奖品图"
                    cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                  />
                  {!activityEditDisabled() && (
                    <Table.Column
                      title="操作"
                      width={130}
                      cell={(val, index, _) => (
                        <FormItem>
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              let row = formData.prizeDay[index];
                              if (row.prizeName === '') {
                                row = null;
                              }
                              setEditValue(row);
                              setTarget(index);
                              setTableName('prizeDay');
                              setVisible(true);
                            }}
                          >
                            <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                          </Button>
                        </FormItem>
                      )}
                    />
                  )}
                </Table>
              </FormItem>
            )}
          </FormItem>
          <FormItem required label="累计/连续奖品设置">
            <FormItem>
              <Radio.Group value={formData.signType} onChange={(signType) => setData({ signType })}>
                <Radio value={1}>累计签到</Radio>
                <Radio value={2}>连续签到</Radio>
              </Radio.Group>
            </FormItem>
            {!activityEditDisabled() && (
              <FormItem>
                <Button
                  disabled={formData.prizeList.length >= 10}
                  type="primary"
                  onClick={() => {
                    formData.prizeList.push(deepCopy(PRIZE_INFO));
                    setData(formData);
                  }}
                >
                  +添加奖品（{formData.prizeList.length}/10）
                </Button>
              </FormItem>
            )}
            <FormItem>
              <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
                <Table.Column
                  title={formData.signType === 1 ? '累计签到天数' : '连续签到天数'}
                  cell={(_, index, row) => (
                    <FormItem required requiredMessage="请输入签到天数">
                      <NumberPicker
                        min={1}
                        max={9999999}
                        step={1}
                        type="inline"
                        value={row.signDay}
                        onChange={(signDay: number) => {
                          formData.prizeList[index].signDay = signDay;
                          setData(formData);
                        }}
                        name={`signDay-${index}`}
                      />
                    </FormItem>
                  )}
                />
                <Table.Column title="奖品名称" dataIndex="prizeName" />
                <Table.Column
                  title="奖品类型"
                  cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                  dataIndex="prizeType"
                />
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }}
                />
                <Table.Column
                  title="发放份数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="单份价值(元)"
                  cell={(_, index, row) => (
                    <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                  )}
                />
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <FormItem disabled={isDisableSetPrize(formData.prizeList, index)}>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            setSignDaySto(formData.prizeList[index].signDay);
                            let row = formData.prizeList[index];
                            if (row.prizeName === '') {
                              row = null;
                            }
                            setEditValue(row);
                            setTarget(index);
                            setTableName('prizeList');
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        {formData.prizeList.length > 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认删除该奖品？',
                                onOk: () => {
                                  formData.prizeList.splice(index, 1);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        )}
                      </FormItem>
                    )}
                  />
                )}
              </Table>
              <div className={styles.tip}>
                领奖限制：当符合累计/连续签到奖励和每日签到奖励时，优先发放累计/连续签到奖励。
                <br />
                {formData.signType === 1
                  ? '参与次数：达到累计签到天数并领取奖品后，签到记录不清零。如：累计签到5天并领取奖品，则第6天的累计签到记录是6天。'
                  : '参与次数：每个人领取的连续签到奖励次数均为1次。'}
              </div>
            </FormItem>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
          isBosideng
          defaultTarget={2}
          typeList={[1, 2, 3, 4, 6, 7]}
        />
      </LzDialog>
    </div>
  );
};
