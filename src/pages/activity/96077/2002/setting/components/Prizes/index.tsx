/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Message, NumberPicker } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import YiLiChoosePrize from '@/components/YiLiChoosePrize';
import { activityEditDisabled, getParams, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import { getPrizeRemain } from '@/api/v96077';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  const [multipleVisible, setMultipleVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  const onMultiplePrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.multiplePrizeList[target] = data;
    // 计算总概率
    formData.totalProbability = formData.multiplePrizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+formData.totalProbability * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      formData.multiplePrizeList.splice(target, 1, PRIZE_INFO);
      return false;
    }
    setData(formData);
    setMultipleVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const multiplePrizeListLength = formData.multiplePrizeList.length;
    // 生成默认奖品列表
    const list3: PrizeInfo[] = [...formData.multiplePrizeList];
    for (let i = 0; i < 2 - multiplePrizeListLength; i++) {
      list3.push(PRIZE_INFO);
    }
    setData({
      multiplePrizeList: list3.length ? list3 : formData.multiplePrizeList,
    });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onMultipleCancel = (): void => {
    setMultipleVisible(false);
  };

  const editPrizes = async (prize: any, index: number) => {
    let row = prize[index];
    if (row.prizeName === '谢谢参与' || !row.prizeName) {
      row = null;
    } else if (getParams('type') === 'edit') {
      try {
        const data = await getPrizeRemain({
          prizeType: row.prizeType,
          activityId: getParams('id'),
          prizeKey: row.prizeKey,
        });
        const keyMap = {
          1: 'quantityRemain',
          2: 'quantityRemain',
          3: 'quantityAvailable',
          6: 'quantityRemain',
          7: 'cardSurplus',
          8: 'quantityRemain',
          9: 'quantityRemain',
          10: 'quantityRemain',
          12: 'quantityRemain',
        };
        row[keyMap[row.prizeType]] = +data >= 0 ? +data : 0;
      } catch (error) {
        console.error(error);
      }
    }
    setEditValue(row || null);
    setTarget(index);
  };

  // 首购权益1奖品修改
  const editMultiplePrize = async (index: number) => {
    await editPrizes(formData.multiplePrizeList, index);
    setMultipleVisible(true);
  };

  return (
    <div>
      <LzPanel title="奖项设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem
            label="首购权益1设置"
            required
            // extra={
            //   <div className="next-form-item-help">
            //     提示：用户订单满足上述条件后获得领奖资格，领取奖励后，待订单完成，系统将自动发放奖励；领取上限为1份/人
            //   </div>
            // }
          >
            <Table dataSource={formData.multiplePrizeList}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />

              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem disabled={isDisableSetPrize(formData.multiplePrizeList, index)}>
                    <Button text type="primary" onClick={() => editMultiplePrize(index)}>
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {!activityEditDisabled() && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            if (_.prizeType) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空该奖品？',
                                onOk: () => {
                                  formData.multiplePrizeList.splice(index, 1, PRIZE_INFO);
                                  formData.totalProbability = formData.multiplePrizeList
                                    .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
                                    .reduce((v, total) => {
                                      return v + Number(total.probability);
                                    }, 0);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      </>
                    )}
                  </FormItem>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="权益1最大领取份数" required requiredMessage="请输入权益1最大领取份数">
            <NumberPicker
              name="receiveNum"
              min={1}
              max={2}
              type="inline"
              value={formData.receiveNum}
              onChange={(receiveNum: number) => setData({ receiveNum })}
            />{' '}
            份
          </FormItem>
        </Form>
      </LzPanel>
      <LzDialog
        title={false}
        visible={multipleVisible}
        footer={false}
        onClose={() => setMultipleVisible(false)}
        style={{ width: '670px' }}
      >
        <YiLiChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onMultiplePrizeChange}
          typeList={[2, 6]}
          defaultTarget={2}
          onCancel={onMultipleCancel}
          hasLimit={false}
          hasProbability={false}
        />
      </LzDialog>
    </div>
  );
};
