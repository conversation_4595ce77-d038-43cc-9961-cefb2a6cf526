/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

// eslint-disable-next-line complexity
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        {/* <FormItem label="参与者生成人群包">{formData.crowdPackage ? '开启' : '关闭'}</FormItem> */}

        {/* <FormItem label="邀请有礼奖品配置">{formData.isInvite === 2 ? '配置' : '不配置'}</FormItem> */}

        {formData.isInvite === 2 && (
          <FormItem label="奖项类型">{formData.prizeSendType === 1 ? '单一型奖品配置' : '阶梯型奖品配置'}</FormItem>
        )}

        {formData.isInvite === 2 && (
          <FormItem label="邀请有礼奖品设置" isPreview={false}>
            <Table
              dataSource={formData.prizeList.filter((e) => e.prizeName !== '谢谢参与' || !e.prizeName)}
              style={{ marginTop: '15px' }}
            >
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="邀请入会人数" cell={(_, index, row) => <div>{row.peopleNum}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => (
                  <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                    {PRIZE_TYPE[row.prizeType]}
                  </div>
                )}
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              {formData.prizeSendType !== 1 && (
                <Table.Column
                  title="单份价值(元)"
                  cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : '0'}</div>}
                />
              )}
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
            </Table>
          </FormItem>
        )}
        {formData.prizeSendType === 2 && formData.isInvite === 2 && <FormItem label="同一奖品">最多领取一次</FormItem>}
        {formData.prizeSendType === 1 && formData.isInvite === 2 && (
          <FormItem label="每人每天最多中奖次数">
            {formData.winLotteryDayType === 1 && '不限制'}
            {formData.winLotteryDayType === 2 && `限制每天内用户最多中奖${formData.winLotteryDayCounts}次`}
          </FormItem>
        )}
        <FormItem label="是否开启邀请排榜奖品设置">{formData.isRank === 1 ? '开启排行榜' : '未开启排行榜'}</FormItem>
        {formData.isRank === 1 && (
          <FormItem label="排行榜统计时间">
            {format.formatDateTimeDayjs(formData.statisticsStartTime)}至
            {format.formatDateTimeDayjs(formData.statisticsEndTime)}
          </FormItem>
        )}
        {formData.isRank === 1 && (
          <FormItem label="排行榜奖励发放时间">
            {format.formatDateTimeDayjs(formData.rankStartTime)}至{format.formatDateTimeDayjs(formData.rankEndTime)}
          </FormItem>
        )}
        {formData.isRank === 1 && (
          <FormItem label="奖品列表" isPreview={false}>
            <Table
              dataSource={formData.prizeRankList.filter((e) => e.prizeName !== '谢谢参与')}
              style={{ marginTop: '15px' }}
            >
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="获奖排名" cell={(_, index, row) => <div>排名第{row.rank}名</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => (
                  <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                    {PRIZE_TYPE[row.prizeType]}
                  </div>
                )}
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : '0'}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
            </Table>
          </FormItem>
        )}
        {/* <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )} */}
        <FormItem label="规则内容">
          <Input.TextArea value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
