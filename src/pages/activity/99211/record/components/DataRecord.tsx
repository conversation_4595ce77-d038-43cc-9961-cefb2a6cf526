import React, { useEffect, useState } from 'react';
import { Form, DatePicker2, Field, Table, Button, Icon, Balloon } from "@alifd/next";
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataSingleAct, dataSingleActExport, } from '@/api/v99211';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default () => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs().format('YYYY-MM-DD 23:59:59'),
  ];

  const MoveTarget = <Icon size="xs" type="help" id="top" style={{ marginLeft: '5px' }} />;

  const setTipsTableTitle = (title: string, moreTxt: string) => {
    return (
      <div style={{display: 'flex',alignItems:'center',justifyContent: 'center'}}>
        {title}
        <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover" closable={false}>
          {moreTxt}
        </Balloon>
      </div>
    );
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataSingleAct(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataSingleActExport(formValue).then((data: any) => downloadExcel(data, '活动数据'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="查询时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={200} align={'center'} title="日期" dataIndex="日期" lock={'left'} />
        <Table.Column width={200} align={'center'} dataIndex="活动PV" title={() => setTipsTableTitle('活动PV', '不去重')} />
        <Table.Column width={200} align={'center'} dataIndex="活动UV" title={() => setTipsTableTitle('活动UV', '单日去重')} />
        <Table.Column width={200} align={'center'} dataIndex="入会人数" title={() => setTipsTableTitle('入会人数', '活动期间去重')} />
        <Table.Column width={200} align={'center'} dataIndex="每日新进入活动人数" title={() => setTipsTableTitle('每日新进入活动人数', '活动期间去重')} />
        <Table.Column width={200} align={'center'} dataIndex="每日新参与活动人数" title={() => setTipsTableTitle('每日新参与活动人数', '访问且参与活动，活动期间去重，参与活动指互动，看规则不算')} />
        <Table.Column width={200} align={'center'} title="签到人数" dataIndex="签到人数" />
        <Table.Column width={200} align={'center'} title="签到次数" dataIndex="签到次数" />
        <Table.Column width={200} align={'center'} dataIndex="参与答题人数" title={() => setTipsTableTitle('参与答题人数', '单日去重')} />
        <Table.Column width={200} align={'center'} title="参与答题次数" dataIndex="参与答题次数" />
        <Table.Column width={200} align={'center'} dataIndex="抽奖人数" title={() => setTipsTableTitle('抽奖人数', '单日去重')} />
        <Table.Column width={200} align={'center'} title="许愿人数" dataIndex="许愿人数" />
        <Table.Column width={200} align={'center'} dataIndex="许愿次数" title={() => setTipsTableTitle('许愿次数', '一下许愿多次的算多次')} />
        <Table.Column width={200} align={'center'} title="许愿人数" dataIndex="许愿人数" />
        <Table.Column width={200} align={'center'} title="抽奖次数" dataIndex="抽奖次数" />
        <Table.Column width={200} align={'center'} title="全套兑换人数" dataIndex="全套兑换人数" />
        <Table.Column width={200} align={'center'} title="全套兑换次数" dataIndex="全套兑换次数" />
        <Table.Column width={200} align={'center'} title="普通兑换人数" dataIndex="普通兑换人数" />
        <Table.Column width={200} align={'center'} title="普通兑换次数" dataIndex="普通兑换次数" />
        <Table.Column width={200} align={'center'} title="翻卡人数" dataIndex="翻卡人数" />
        <Table.Column width={200} align={'center'} title="翻卡次数" dataIndex="翻卡次数" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
