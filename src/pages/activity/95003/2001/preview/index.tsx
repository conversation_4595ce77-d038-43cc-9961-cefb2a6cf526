import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        {/* <FormItem label="参与时段限制"> */}
        {/*  {formData.partake === 1 ? `${formData.partakeStartTime}至${formData.partakeEndTime}` : '关闭'} */}
        {/* </FormItem> */}
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        {/* <FormItem label="活动生成人群包">{`${formData.crowdPackage === 1 ? '开启' : '关闭'}`}</FormItem> */}
        <FormItem label="订单结算周期">{`${format.formatDateTimeDayjs(
          formData.orderStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderEndTime)}`}</FormItem>
        <FormItem label="订单价格结算方式">实付金额</FormItem>
        <FormItem label="订单结算状态">{formData.orderStatus === 1 ? '交易完成' : '已付款'}</FormItem>
        <FormItem label="订单金额计算方式">累计金额</FormItem>
        {/* <FormItem label="订单拆单后是否合并">{formData.split ? '是' : '否'}</FormItem> */}
        {/* <FormItem label="订单金额">大于等于{formData.orderRestrainAmount}元</FormItem> */}
        {/* <FormItem label="订单商品"> */}
        {/*  {formData.orderSkuisExposure === 0 && <div>全部商品</div>} */}
        {/*  {formData.orderSkuisExposure === 1 && ( */}
        {/*    <div className={styles.container}> */}
        {/*      {formData.orderSkuList?.map((sku, index) => { */}
        {/*        return ( */}
        {/*          <div className={styles.skuContainer}> */}
        {/*            <img className={styles.skuImg} src={sku.skuMainPicture} alt="" /> */}
        {/*            <div> */}
        {/*              <div className={styles.skuName}>{sku.skuName}</div> */}
        {/*              <div className={styles.skuId}>SKUID:{sku.skuId}</div> */}
        {/*              <div className={styles.price}>¥ {sku.jdPrice}</div> */}
        {/*            </div> */}
        {/*          </div> */}
        {/*        ); */}
        {/*      })} */}
        {/*    </div> */}
        {/*  )} */}
        {/* </FormItem> */}

        <FormItem label="抽奖时间">{`${format.formatDateTimeDayjs(
          formData.drawStartTime,
        )}至${format.formatDateTimeDayjs(formData.drawEndTime)}`}</FormItem>
        <FormItem label="抽奖机会" isPreview={false}>
          {formData.drawTimesType === 1 && (
            <div>
              消费满{formData.drawTimesList[0].orderPrice}元，可获得{formData.drawTimesList[0].drawNum}
              次抽奖机会
            </div>
          )}
          {formData.drawTimesType === 2 && (
            <Table dataSource={formData.drawTimesList} style={{ marginTop: '15px' }}>
              <Table.Column title="阶梯等级" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="累计消费金额(元)" cell={(_, index, row) => <div>{row.orderPrice}</div>} />
              <Table.Column title="抽奖机会(次)" cell={(_, index, row) => <div> {row.drawNum}</div>} />
            </Table>
          )}
        </FormItem>
        <FormItem label="每人每天最多获奖次数">
          {formData.drawLimit === 1 && '不限制'}
          {formData.drawLimit === 2 && `限制每天内用户最多中奖${formData.drawNum}次`}
        </FormItem>
        <FormItem label="每人累计最多中奖次数">
          {formData.winLotteryLimit === 1 && '不限制'}
          {formData.winLotteryLimit === 2 && `限制活动周期内用户最多中奖${formData.winLotteryNum}次`}
        </FormItem>
        <FormItem label="奖品列表" isPreview={false}>
          <Table
            dataSource={formData.prizeList.filter((e) => e.prizeName !== '谢谢参与')}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.prizeType ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="中奖概率(%)"
              cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>}
            />

            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        {/* <FormItem label="是否添加曝光商品"> */}
        {/*  {formData.isExposure === 0 && <div>否</div>} */}
        {/*  {formData.isExposure === 1 && ( */}
        {/*    <div className={styles.container}> */}
        {/*      {formData.skuList?.map((sku, index) => { */}
        {/*        return ( */}
        {/*          <div className={styles.skuContainer}> */}
        {/*            <img className={styles.skuImg} src={sku.skuMainPicture} alt="" /> */}
        {/*            <div> */}
        {/*              <div className={styles.skuName}>{sku.skuName}</div> */}
        {/*              <div className={styles.skuId}>SKUID:{sku.skuId}</div> */}
        {/*              <div className={styles.price}>¥ {sku.jdPrice}</div> */}
        {/*            </div> */}
        {/*          </div> */}
        {/*        ); */}
        {/*      })} */}
        {/*    </div> */}
        {/*  )} */}
        {/* </FormItem> */}
        <FormItem label="活动分享">{formData.shareStatus == 1 ? '开启' : '关闭'}</FormItem>
        {formData.shareStatus == 1 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <div className="rule-word-break" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
            {formData.rules}
          </div>
        </FormItem>
      </Form>
    </div>
  );
};
