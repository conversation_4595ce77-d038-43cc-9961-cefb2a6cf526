import { useState } from 'react';
import * as React from 'react';
import { Button, Form, Input } from '@alifd/next';

interface FormData {
  planName: string;
  planId: string;
}
const initBeanSearchData: FormData = {
  planName: '',
  planId: '',
};

export default ({ onSearch }: any) => {
  const [beanSearchData, setBeanSearchData] = useState(initBeanSearchData);

  /**
   * 京豆筛选框赋值
   * @param {*} value 筛选值
   */
  const beanSelectData = (value: any) => {
    setBeanSearchData({
      ...beanSearchData,
      ...value,
    });
  };

  const onSearchClick = () => {
    onSearch(beanSearchData);
  };

  const onResetClick = () => {
    setBeanSearchData(initBeanSearchData);
    onSearch(initBeanSearchData);
  };

  return (
    <div>
      <Form inline>
        <Form.Item className="item" label="爱奇艺会员计划名称：">
          <Input
            value={beanSearchData.planName}
            className="dialog-search-ctrl"
            placeholder="请输入计划名称"
            onChange={(planName) => beanSelectData({ planName })}
          />
        </Form.Item>
        <Form.Item className="item" label="爱奇艺会员计划ID：">
          <Input
            value={beanSearchData.planId}
            className="dialog-search-ctrl"
            placeholder="请输入计划ID"
            onChange={(planId) => beanSelectData({ planId })}
          />
        </Form.Item>
        <Form.Item className="item" style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSearchClick}>
            查询
          </Button>
          <Button style={{ marginLeft: 10 }} type="normal" onClick={onResetClick}>
            重置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
