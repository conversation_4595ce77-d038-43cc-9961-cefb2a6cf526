/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  potNum: string;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  prizeKey: string;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface seriesSkuList {
  skuId: string;
  tankNum: number;
}

export interface SeriesList {
  seriesName: string;
  stepList: StepList[];
}

export interface StepList {
  stepName: string;
  tankNum: string;
  prizeList: PrizeInfo[];
}

export interface CustomValue {
  pageBg: string;
  actBg: string;
  actBgColor: string;
  ruleBtn: string;
  myPrizeBtn: string;
  stepBtnBg: string;
  stepBtnSelectBg: string;
  exchangeBtn: string;
  skuListBg: string;
  skuBg: string;
  skuTitleBg: string;
  skuTitleColor: string;
  goSkuBtn: string;
  ruleImg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;

  joinMemberBk: string;
  ruleTextColor: string;
  ruleBk: string;
  myPrizeBk: string;
  myPrizeTextColor: string;
  confirmPrizeBk: string;
  receiveSuccessBk: string;
  saveAddressBk: string;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  limitOrderTimeType: number;
  orderBeforeStartTime: string;
  orderBeforeEndTime: string;
  orderAfterStartTime: string;
  orderAfterEndTime: string;
  seriesList: SeriesList[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;
  // 限制订单状态
  orderRestrainStatus: number;
  // 是否延迟发奖
  isDelayedDisttribution: number;
  // 延迟发奖天数
  awardDays: number;
  seriesPrizeList: any[];
  fileName: string;
  fileList: [];
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/280479/30/9795/204082/67e3d0e6F1a532c02/67120058d4ca9c0c.png',
  actBg: '',
  actBgColor: '#acceee',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/278047/26/10502/6649/67e3d0eaF038c932f/8fe47b02db726ef8.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/281865/19/9537/6689/67e3d0eaF5ba97dfb/3bb9b09f7aa9a918.png',
  stepBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/282098/22/10021/5174/67e3d0ebF2e64e17a/09490bc285578600.png',
  stepBtnSelectBg: '//img10.360buyimg.com/imgzone/jfs/t1/273283/18/10200/6961/67e3d0eaF8a1bb1de/e5a7676f635fb405.png',
  exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/283267/30/9342/31835/67e3d0eaF5ba6070e/b06a6fdb7bae72fc.png',
  skuListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/272905/33/10644/66949/67e3d0eeF22d856e7/1bc88fcf9aef3f26.png',
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274665/1/10496/22030/67e3d0f0F9b33dff3/ff6b46f4500b8c9e.png',
  skuTitleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/282457/13/9819/4782/67e3d080F48d622f2/51ca9254929a8525.png',
  skuTitleColor: '#fff',
  goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/276655/30/10601/10922/67e3d0ebFa5be6d89/48215bd8c76950bd.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/280485/8/9604/66722/67e50288F0793202c/ee1cbfa369b5355e.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/270766/9/11668/12372/67e50289F8b081b22/e07368acebb4e3f9.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/280178/5/10456/86748/67e50288F51853cbc/6afd4e817aa3e456.png',
  ruleImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/278954/23/10495/458502/67e3d0eeF479cc718/354f0c78e5bf94ce.png',

  joinMemberBk: '//img10.360buyimg.com/imgzone/jfs/t1/279409/32/10939/99238/67e60f75F3379d609/2025b76ba3b1faed.png',
  ruleBk: '//img10.360buyimg.com/imgzone/jfs/t1/284873/12/9386/25620/67e3c4e6F54cd82ab/f1741fea5b99a780.png',
  ruleTextColor: '#f02816',
  myPrizeTextColor: '#000',
  myPrizeBk: '//img10.360buyimg.com/imgzone/jfs/t1/285088/21/10900/62918/67e60f74F2d5e8fad/78eed8ed41bb98e7.png',
  confirmPrizeBk: '//img10.360buyimg.com/imgzone/jfs/t1/283775/19/10655/27943/67e60f76F4a6eed6d/cff6471310fabba7.png',
  receiveSuccessBk:
    '//img10.360buyimg.com/imgzone/jfs/t1/280609/32/10410/101350/67e60f76F7d49989e/4d5e1aedec65f7c5.png',
  saveAddressBk: '//img10.360buyimg.com/imgzone/jfs/t1/280129/26/9948/29752/67e3c4e5Ff37e998e/53734772885538a2.png',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  const now = (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00');
  const now1 = (dayjs().subtract(1, 'day') as any).format('YYYY-MM-DD 23:59:59');
  const after30 = (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59');
  const beforeOneYear = (dayjs(after30).subtract(1, 'year') as any).format('YYYY-MM-DD 00:00:00');
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `会员转段赠好礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [now, after30],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(now),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(after30),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    limitOrderTimeType: 1,
    orderBeforeStartTime: format.formatDateTimeDayjs(beforeOneYear),
    orderBeforeEndTime: format.formatDateTimeDayjs(now1),
    orderAfterStartTime: format.formatDateTimeDayjs(now),
    orderAfterEndTime: format.formatDateTimeDayjs(after30),
    // 系列及奖品列表
    seriesList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '会员转段赠好礼，超多惊喜大奖等你来领！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    // 模板code
    templateCode: '',
    // 会员等级标签
    gradeLabel: [],
    // 人群包
    crowdBag: '',
    // 限制订单状态
    orderRestrainStatus: 1,
    // 是否延迟发奖
    isDelayedDisttribution: 0,
    // 延迟延迟发奖天数
    awardDays: 0,
    seriesPrizeList: [],
    fileName: '',
    fileList: [],
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  potNum: '',
  ifPlan: 0,
  prizeKey: '',
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.orderAfterStartTime).add(formData.awardDays, 'day').isBefore(dayjs(start));
    if (isStart) {
      Message.error(
        `奖品${prize.prizeName}起始时间需要早于或等于订单开始时间${formData.awardDays ? `+延迟发奖天数` : ''}`,
      );
      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const delayeTime = dayjs(formData.orderAfterEndTime).add(formData.awardDays, 'day');
    const isEnd: boolean = dayjs(delayeTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(
        `奖品${prize.prizeName}结束时间需要晚于或等于订单结束时间${formData.awardDays ? `+延迟发奖天数` : ''}`,
      );
      return false;
    }
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {
  if (!formData.seriesList.length) {
    Message.error('请上传系列数据');
    return false;
  }
  for (let i = 0; i < formData.seriesPrizeList.length; i++) {
    if (!formData.seriesPrizeList[i].seriesName) {
      Message.error(`请填写奖项${i + 1}系列名称`);
      return false;
    }
    if (!formData.seriesPrizeList[i].seriesPic) {
      Message.error(`请上传奖项${i + 1}系列图片`);
      return false;
    }
    if (!formData.seriesPrizeList[i].stepSetting) {
      Message.error(`请选择奖项${i + 1}的阶段设置`);
      return false;
    }
    for (let j = 0; j < formData.seriesPrizeList[i].stepSetting.length; j++) {
      if (!formData.seriesPrizeList[i].stepSetting[j].stepImg) {
        Message.error(`请上传奖项${i + 1}阶段${j + 1}图片`);
        return false;
      }
    }
    if (!formData.seriesPrizeList[i].beforeOptions.length) {
      Message.error(`请选择奖项${i + 1}转段前商品`);
      return false;
    }
    if (!formData.seriesPrizeList[i].afterOptions) {
      Message.error(`请选择奖项${i + 1}转段后商品`);
      return false;
    }
    if (!formData.seriesPrizeList[i].prizeList.length) {
      Message.error(`请上传奖项${i + 1}奖品`);
      return false;
    }
    for (let j = 0; j < formData.seriesPrizeList[i].prizeList.length; j++) {
      const prize = formData.seriesPrizeList[i].prizeList[j];
      if (!prize.prizeName) {
        Message.error(`请选择奖项${i + 1}的第${j + 1}个奖品`);
        return false;
      }
      if (!prize.step) {
        Message.error(`请选择奖项${i + 1}的${j + 1}的所属阶段`);
        return false;
      }
      if (!isPrizeValid(prize, formData)) {
        return false;
      }
    }
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType() && +getParams('status') === 2) {
    return true;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (!formData.seriesList.length) {
    Message.error('请上传系列数据');
    return false;
  }
  if (!hasPrize(formData)) {
    return false;
  }

  return true;
};
