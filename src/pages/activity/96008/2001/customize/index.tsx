import React from 'react';
import { CustomValue } from '../util';
// 基础信息
import Base from './components/Base';
import Series from './components/Series';
import { Tab } from '@alifd/next';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  handleChangeActiveKey: (activeKey: string) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { defaultValue, value, handleChange } = props;
  const [popupActiveKey, setPopupActiveKey] = React.useState('index');
  const handleTabChange = (key: string): void => {
    setPopupActiveKey(key);
    props.handleChangeActiveKey(key);
  };
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab activeKey={popupActiveKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Base {...eventProps} />
        <Series {...eventProps} />
      </Tab>
    </div>
  );
};
