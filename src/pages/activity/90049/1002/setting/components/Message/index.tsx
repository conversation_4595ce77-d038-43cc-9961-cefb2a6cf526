/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, { useImperative<PERSON>andle, useReducer, useEffect, useState, Props } from 'react';
import { Form, Button, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';

import {
  checkActivityData,
  FormLayout,
  generateMembershipString,
  PageData,
  PrizeInfo,
} from '@/pages/activity/90049/1002/util';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
export default (props: any) => {
  const { smsInfo } = props;
  console.log('🚀 ~ smsInfo:', smsInfo);
  const field: Field = Field.useField();

  return (
    <div>
      <LzPanel title="短信">
        <Form {...formItemLayout} field={field}>
          <FormItem label="短信内容">
            <Input.TextArea
              value={smsInfo}
              readOnly={true}
              name="rules"
              autoHeight={{ minRows: 8, maxRows: 40 }}
              className="form-input-ctrl"
            />
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
