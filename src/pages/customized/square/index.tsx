import React, { useEffect, useState } from 'react';
import styles from './style.module.scss';
import ResponsiveList from './components/ResponsiveList';
import { getActivitySquare } from '@/api/customized';
import { CommonSquareFilterResponse } from '@/api/types';
import { Loading } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { removeOlderInteractSessionStorage } from '@/utils';
import LzImg from '@/components/LzImg';

export default () => {
  const [list, setList] = useState<CommonSquareFilterResponse[] | undefined>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [strategy, setStrategy] = useState<boolean>(false);

  const getSysApiLogList = async () => {
    try {
      setLoading(true);
      const result: CommonSquareFilterResponse[] = await getActivitySquare({});
      setList(result);
    } catch (e) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  };

  useEffect((): void => {
    console.log(1);
    getSysApiLogList().then();
    removeOlderInteractSessionStorage();
  }, []);
  return (
    <div className={styles.SquarePage}>
      <LzPanel title="定制活动广场" subTitle="基于云鹿的专属B+C定制活动广场">
        <Loading visible={loading} inline={false}>
          <ResponsiveList list={list} is-loading={loading} />
        </Loading>
      </LzPanel>
      {strategy && (
        <div className={styles.dialogWrap}>
          <div className={styles.visibleImage}>
            <LzImg
              src={
                'https://img10.360buyimg.com/imgzone/jfs/t1/183726/4/39497/317526/654b2781Fd5a6d0c2/1f91217a838982be.png'
              }
            />
            <div className={styles.visibleCloseBtn} onClick={() => setStrategy(false)}>
              <i className="iconfont icon-icon4-29" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
