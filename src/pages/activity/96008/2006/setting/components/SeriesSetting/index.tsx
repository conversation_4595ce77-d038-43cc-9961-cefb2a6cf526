/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024-4-8 9:20
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData, SeriesList } from '../../../util';
import { Button, Dialog, Field, Form, Message, NumberPicker, Radio, Upload } from '@alifd/next';
// 奖品信息
import { deepCopy, downloadExcel, activityEditDisabled } from '@/utils';
import { templateExport } from '@/api/v99101';
import { config } from 'ice';
import CONST from '@/utils/constant';
import { importSkuText } from '@/api/sku';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const field: Field = Field.useField();
  const [uploaderRef, setUploaderRef] = useState(false);
  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };

  const downloadTemplate = async () => {
    try {
      const data: any = await templateExport();
      downloadExcel(data, '商品导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  const prizesRef: any[] = [];
  useImperativeHandle(sRef, (): { submit: () => object | null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        if (errors) {
          err = errors;
        }
      });
      for (let i = 0; i < prizesRef.length; i++) {
        const e = prizesRef[i].submit();
        if (e) {
          err = e;
        }
      }
      return err;
    },
  }));

  const setTemporarySeriesList = async (data) => {
    const getSkuInfo = async (list, index) => {
      const res = await importSkuText({
        skuIdCount: list
          .filter((e) => e.isShow == 1)
          .map((item) => item.skuId)
          .filter((e) => e)
          .splice(0, 10),
      });
      const successListArr = res.successList;
      const errorListArr = res.errorList;
      // if (successListArr && errorListArr) {
      //   successListArr?.push(...errorListArr);
      // }
      data[index].previewSkuList = successListArr || [];
    };
    await Promise.all(data.map((item, index) => getSkuInfo(item.skuList, index)));
    formData.seriesList = [];
    data.forEach((item) => {
      formData.seriesList.push({
        period: item.period,
        periodSort: item.periodSort,
        skuList: item.skuList,
        previewSkuList: item.previewSkuList,
      });
    });
    formData.seriesPrizeList.push({
      seriesName: '',
      seriesPic: '',
      beforeOptions: [],
      afterOptions: [],
      prizeList: [],
      beforeSkuList: [],
      afterSkuList: [],
      stepSetting: [
        {
          step: 1,
          minPotNum: 1,
          maxPotNum: 5,
        },
      ],
    });
    setData(formData);
    Dialog.success({
      title: '导入结果',
      content: (
        <div>
          <p>导入成功</p>
        </div>
      ),
      onOk: () => {
        console.log('导入成功');
      },
    });
  };

  return (
    <>
      <LzPanel title="数据源设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="系列源上传">
            <Message type="notice" style={{ marginBottom: 10 }}>
              导入须知： <br />
              1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
              <br />
              2.单次导入最大5M，导入中请不要关闭此页面。
              <br />
              <Button text type="primary" onClick={downloadTemplate} disabled={activityEditDisabled()}>
                下载模板
              </Button>
            </Message>

            <Upload
              disabled={activityEditDisabled()}
              action={`${config.baseURL}/99101/template/import`}
              name="file"
              method="post"
              headers={{
                token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
                prd: localStorage.getItem(CONST.LZ_SSO_PRD),
              }}
              ref={saveUploaderRef}
              value={formData.fileList}
              fileNameRender={(file) => <span>{formData.fileName}</span>}
              limit={1}
              listType="text"
              accept=".xls,.xlsx"
              onChange={(info) => {
                if (info.length) {
                  if (info[0].size > 5 * 1024 * 1024) {
                    Message.error('文件大小不能超过5M');
                    return;
                  }
                }
                console.log(info[0].name, 'info[0].name');
                prizesRef.splice(0);
                setData({ fileName: info[0].name, fileList: info });
              }}
              onError={(res) => {
                if (res.state === 'error') {
                  if (res.response?.message) {
                    Message.error(res.response?.message);
                  } else {
                    Message.error('文件错误，请上传正确的文件');
                  }
                }
              }}
              onRemove={(file) => {
                setData({ fileList: [], seriesPrizeList: [] });
              }}
              onSuccess={(res) => {
                if (res.response.code === 200) {
                  setTemporarySeriesList(res.response.data);
                } else if (res.response?.message) {
                  setData({ fileList: [] });
                  Message.error(res.response?.message);
                } else {
                  setData({ fileList: [] });
                  Message.error('文件错误，请上传正确的文件');
                }
              }}
              style={{ marginBottom: 10 }}
            >
              <div className="next-upload-drag">
                <p className="next-upload-drag-icon">
                  <Button type="primary" disabled={activityEditDisabled()}>
                    上传系列数据
                  </Button>
                </p>
                <p className="next-upload-drag-hint">支持xls类型的文件</p>
              </div>
            </Upload>
          </FormItem>
        </Form>
      </LzPanel>
    </>
  );
};
