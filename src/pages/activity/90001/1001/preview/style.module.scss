.preview {
  .value {
    display: flex;
    align-items: center;
  }
}
.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  .skuContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;

    .skuImg {
      width: 80px;
      height: 80px;
    }
    .skuName {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .skuId {
      color: lightgray;
      font-size: 12px;
    }
    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}
.skuListImage{
  width: 400px;
  height: 200px;
}
.skuListDiv{
  display: -webkit-box;
  margin-top: 15px;
  display: flex;
}
.prizeListDiv11{
  display: -webkit-box;
  margin-top: 15px;
}
.skuImageDiv{
  display: -webkit-box;
  margin-top: 15px;
}
.prizeListDiv{
  display: flex;
  margin-top: 15px;
}
.labelTitle{
  width: 120px;
  text-align: right;
}
.labelContent{
  width: calc(100% - 120px);
}
.textAreaStyle{
  width: 100%;
  color: #1f2633;
  font-size: 12px;
  line-height: 28px;
}
