import React, { useEffect, useState } from 'react';
import { Form, Input, Field, Table, Button, Message } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataJoinLog, dataSignLogExport, dataJoinLogUploadPin } from '@/api/v80003';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';
import SignRecord from './SignRecord';
import styles from './style.module.scss';

const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [selectUserVisible, setSelectUserVisible] = useState(false);
  const [selectUserId, setSelectUserId] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataJoinLog(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataSignLogExport(formValue).then((data: any) => downloadExcel(data, '参与记录'));
  };
  const encryptStr = (pin: string): string => {
    const first = pin.slice(0, 1);
    const last = pin.slice(-1);
    return `${first}****${last}`;
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
          生成人群包
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        {/* <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        /> */}
        {/* <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        /> */}
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value, index, data) => (
            <div className={styles.inARow}>
              <div className={styles.copyText}>{data.nickName ? encryptStr(data.nickName) : '-'}</div>
              {data.nickName && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    Utils.copyText(data.nickName).then(() => {
                      Message.success('用户昵称已复制到剪切板');
                    });
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value, index, data) => (
            <div className={styles.inARow}>
              <div className={styles.copyText}>{data.encryptPin ? encryptStr(data.encryptPin) : '-'}</div>
              {data.encryptPin && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    Utils.copyText(data.encryptPin).then(() => {
                      Message.success('用户pin已复制到剪切板');
                    });
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column
          title="签到历史"
          cell={(value, index, data) => (
            <Button
              type="primary"
              text
              onClick={() => {
                setSelectUserId(data.id);
                setSelectUserVisible(true);
              }}
            >
              查看
            </Button>
          )}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      <LzDialog
        title="签到历史"
        className="lz-dialog-medium"
        visible={selectUserVisible}
        footer={false}
        onCancel={() => setSelectUserVisible(false)}
        onClose={() => setSelectUserVisible(false)}
      >
        <SignRecord id={selectUserId} />
      </LzDialog>
      <LzDialog
        title="生成人群包"
        className="lz-dialog-mini"
        visible={packVisible}
        footer={false}
        onCancel={() => setPackVisible(false)}
        onClose={() => setPackVisible(false)}
      >
        <LzGenerateCrowdBag
          dataUploadPin={dataJoinLogUploadPin}
          formValue={field.getValues()}
          cancel={() => setPackVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
