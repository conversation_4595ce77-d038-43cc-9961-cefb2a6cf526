import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Radio, Select, NumberPicker } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, getShopOrderStartTime, validateActivityThreshold } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [
  { label: '已付款', value: 0 },
  { label: '已完成', value: 1 },
];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: dayjs(formData.endTime).subtract(180, 'days').startOf('day').valueOf(),
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
      setTimeout(() => {
        field.validate('orderBeforeRestrainRangeData');
      }, 1000);
    });
  }, [formData.endTime]);
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    setData({
      orderAfterStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderAfterEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };
  const handleStatusChange = (orderRestrainStatus) => {
    setData({ orderRestrainStatus });
    if (orderRestrainStatus === 0 && formData.isDelayedDisttribution === 1) {
      setData({ orderRestrainStatus: 0, isDelayedDisttribution: 0, awardDays: 0 });
    }
  };
  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    const orderRestrainStartTime = val[0];
    const orderRestrainEndTime = val[1];
    if (!orderRestrainStartTime || !orderRestrainEndTime) {
      callback('请选择指定时间');
    } else if (dayjs(orderRestrainEndTime).valueOf() > dayjs(formData.endTime).valueOf()) {
      callback('后置订单时间不能晚于活动结束时间');
    } else if (dayjs(formData.orderBeforeEndTime).valueOf() > dayjs(orderRestrainStartTime).valueOf()) {
      callback('后置订单的开始时间不能早于前置订单的结束时间');
    } else {
      callback();
    }
  };
  const validateOrderTimeBefore = (rule, val, callback): void => {
    // const { orderRestrainStartTime, orderRestrainEndTime } = formData;
    const orderRestrainStartTime = val[0];
    const orderRestrainEndTime = val[1];
    if (!orderRestrainStartTime || !orderRestrainEndTime) {
      callback('请选前置订单时间');
    } else if (
      !shopOrderInfo.longTermOrder &&
      dayjs(orderRestrainStartTime).valueOf() < shopOrderInfo.shopOrderStartTime
    ) {
      callback(`前置订单时间不能早于活动结束时间前${shopOrderInfo.orderRetentionDays}天`);
    } else if (dayjs(orderRestrainEndTime).valueOf() > dayjs(formData.endTime).valueOf()) {
      callback('前置订单时间不能晚于活动结束时间');
    } else {
      callback();
    }
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  useEffect(() => {
    setData({
      orderBeforeEndTime: dayjs(formData.startTime).subtract(1, 'second'),
    });
  }, [formData.startTime]);

  useEffect(() => {
    if (formData.limitOrderTimeType === 0) {
      setData({
        orderAfterStartTime: formData.startTime,
        orderAfterEndTime: formData.endTime,
      });
    }
  }, [formData.startTime, formData.endTime, formData.limitOrderTimeType]);

  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem
            label="前置订单时间"
            required
            requiredMessage="请选择前置订单时间"
            validator={validateOrderTimeBefore}
          >
            <RangePicker
              className="w-300"
              name="orderBeforeRestrainRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={[formData.orderBeforeStartTime, formData.orderBeforeEndTime]}
              disabled={[false, true]}
              onChange={(orderBeforeRestrainRangeData) => {
                setData({
                  orderBeforeStartTime: format.formatDateTimeDayjs(orderBeforeRestrainRangeData[0]),
                  orderBeforeEndTime: format.formatDateTimeDayjs(orderBeforeRestrainRangeData[1]),
                });
              }}
              // disabledDate={(date) => {
              //   return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
              // }}
            />
            {/* <div className={styles.tip}> */}
            {/*  注：1、默认支持查询 */}
            {/*  {shopOrderInfo.longTermOrder */}
            {/*    ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后` */}
            {/*    : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`} */}
            {/*  的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。 */}
            {/*  <br /> */}
            {/*  2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。 */}
            {/* </div> */}
          </FormItem>
          <FormItem
            label="后置订单时间类型"
            required
            requiredMessage="请选择前置订单时间"
            disabled={activityEditDisabled()}
          >
            <Radio.Group
              value={formData.limitOrderTimeType}
              onChange={(limitOrderTimeType) => {
                setData({ limitOrderTimeType });
              }}
            >
              <Radio value={0}>同活动时间</Radio>
              <Radio value={1}>指定时间</Radio>
            </Radio.Group>
          </FormItem>
          {formData.limitOrderTimeType === 1 && (
            <>
              <FormItem label="指定时间" required requiredMessage="请选择指定时间" validator={validateOrderTime}>
                <RangePicker
                  className="w-300"
                  name="orderAfterRestrainRangeData"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  value={[formData.orderAfterStartTime, formData.orderAfterEndTime]}
                  onChange={onDataRangeChange}
                  disabledDate={(date) => {
                    return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
                  }}
                />
              </FormItem>
            </>
          )}
          <FormItem
            label="订单状态"
            // extra={
            //   <div className="next-form-item-help">
            //     已付款：用户付款后即可参与活动
            //     <br />
            //     已完成：(1)用户订单完成后才可参与活动。(2)预售商品需要支付尾款方可参与活动
            //   </div>
            // }
          >
            <Select
              dataSource={ORDER_STATUS}
              value={formData.orderRestrainStatus}
              onChange={handleStatusChange}
              disabled
            />
            {/* <div className={styles.orderTypes}> */}
            {/*  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}> */}
            {/*    已付款 */}
            {/*  </span> */}
            {/*  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}> */}
            {/*    待出库 */}
            {/*  </span> */}
            {/*  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}> */}
            {/*    待发货 */}
            {/*  </span> */}
            {/*  <span className={styles.order}>已完成</span> */}
            {/* </div> */}
          </FormItem>
          <FormItem label="奖品延迟发放" required disabled={activityEditDisabled()}>
            <FormItem>
              <Radio.Group
                value={formData.isDelayedDisttribution}
                onChange={(isDelayedDisttribution) =>
                  setData({ isDelayedDisttribution, awardDays: isDelayedDisttribution })
                }
              >
                <Radio value={0}>否</Radio>
                <Radio value={1}>是</Radio>
              </Radio.Group>
            </FormItem>
            {formData.isDelayedDisttribution === 1 && formData.orderRestrainStatus === 1 && (
              <FormItem required requiredMessage="请输入延迟天数">
                延迟发放{' '}
                <NumberPicker
                  name="awardDays"
                  min={1}
                  max={7}
                  type="inline"
                  value={formData.awardDays}
                  onChange={(awardDays: number) => setData({ awardDays })}
                />{' '}
                天
              </FormItem>
            )}
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
