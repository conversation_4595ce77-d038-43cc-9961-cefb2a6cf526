/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Form, Table, Input, Button, Tab, Grid, NumberPicker, Checkbox, Radio } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_INFO, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { activityEditDisabled } from '@/utils';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p: PageData, c: PageData) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);
  const [activeKey, setActiveKey] = useState('0');
  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{formData.threshold === 0 ? '同活动时间' : '店铺会员'}</FormItem>

        <FormItem title="商品及奖品设置">
          <FormItem>
            <Tab activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
              {formData.seriesPrizeList.map((item, TabIndex) => {
                return (
                  <Tab.Item title={` 奖品项${TabIndex + 1}  `} key={TabIndex}>
                    <Form {...formItemLayout} disabled>
                      <FormItem label="系列名" requiredMessage={'请输入系列名称'}>
                        <Input
                          value={item?.seriesName}
                          placeholder="请输入系列名称"
                          name={`seriesName-${TabIndex}`}
                          maxLength={10}
                          showLimitHint
                          className="w-300"
                        />
                      </FormItem>
                      <FormItem requiredMessage={'请上传系列图'} label="系列图">
                        <Grid.Row>
                          <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                            <img style={{ width: '100px', height: '100px' }} src={item?.seriesPic} alt="seriesPic" />
                          </Form.Item>
                        </Grid.Row>
                        <Input className="validateInput" name={`seriesPic-${TabIndex}`} value={item?.seriesPic} />
                      </FormItem>
                      <FormItem requiredMessage={'请进行阶梯配置'} label="阶梯配置">
                        {item.stepSetting?.map((row, index) => {
                          return (
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <FormItem label={`阶梯${row.step}`} colon />
                                <FormItem label={`罐数`} />
                                <FormItem requiredMessage="请输入最少罐数门槛">
                                  <NumberPicker
                                    disabled={activityEditDisabled() || index > 0}
                                    min={Math.max(item.stepSetting[index]?.minPotNum, 1)}
                                    max={99}
                                    step={1}
                                    value={row.minPotNum}
                                    name={`minPotNum-${index}`}
                                  />
                                </FormItem>
                                <FormItem>
                                  <span style={{ padding: '0 10px' }}>至</span>
                                </FormItem>
                                <FormItem requiredMessage="请输入最多罐数门槛">
                                  <NumberPicker
                                    disabled={activityEditDisabled()}
                                    min={Math.max(item.stepSetting[index]?.minPotNum, 1)}
                                    max={Math.min(item.stepSetting[index + 1]?.minPotNum - 1, 99)}
                                    step={1}
                                    value={row.maxPotNum}
                                    name={`maxPotNum-${index}`}
                                  />
                                </FormItem>
                              </div>
                              <div style={{ display: 'flex' }}>
                                <div>阶段奖品图：</div>
                                <img style={{ width: '100px', height: '100px' }} src={row?.stepImg} alt="stepImg" />
                              </div>
                            </div>
                          );
                        })}
                      </FormItem>
                      <FormItem
                        name={`beforeOptions-${TabIndex}`}
                        label="转段前商品"
                        requiredMessage={'请选择转段前商品'}
                      >
                        <Checkbox.Group name={`beforeOptions-${TabIndex}`} value={item.beforeOptions}>
                          {formData.seriesList.map((option: any) => (
                            <Checkbox key={option.periodSort} value={option.periodSort}>
                              {option.period}
                            </Checkbox>
                          ))}
                          <Button
                            disabled={item.beforeOptions?.length === 0}
                            onClick={() => {
                              setSeriesSkuList(item?.beforeSkuList);
                              setSkuVisible(true);
                            }}
                          >
                            查看SKU
                          </Button>
                        </Checkbox.Group>
                      </FormItem>
                      <FormItem
                        label="转段后商品"
                        requiredMessage={'请选择转段后商品'}
                        name={`afterOptions-${TabIndex}`}
                      >
                        <Radio.Group name={`afterOptions-${TabIndex}`} value={item.afterOptions}>
                          {formData.seriesList.map((option: any) => (
                            <Radio
                              key={option.periodSort}
                              value={option.periodSort}
                              disabled={
                                item.beforeOptions?.length === 0 ||
                                Math.max(...item.beforeOptions, 0) >= option.periodSort
                              }
                            >
                              {option.period}
                            </Radio>
                          ))}
                          <Button
                            disabled={item.afterOptions?.length === 0}
                            onClick={() => {
                              setSeriesSkuList(item.afterSkuList);
                              setSkuVisible(true);
                            }}
                          >
                            查看SKU
                          </Button>
                        </Radio.Group>
                      </FormItem>
                      <FormItem label="奖品列表">
                        <FormItem>
                          <Table dataSource={item?.prizeList} style={{ marginTop: '15px' }}>
                            <Table.Column
                              title="所属阶梯"
                              cell={(_, index, row) => (
                                <FormItem requiredMessage="请选择所属阶梯">
                                  <div>{`阶梯${row.step}`}</div>
                                </FormItem>
                              )}
                            />
                            <Table.Column title="奖品名称" dataIndex="prizeName" />
                            <Table.Column
                              title="奖品类型"
                              cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                              dataIndex="prizeType"
                            />
                            <Table.Column
                              title="单位数量"
                              cell={(_, index, row) => {
                                if (row.prizeType === 1) {
                                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                                } else {
                                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                                }
                              }}
                            />
                            <Table.Column
                              title="发放份数"
                              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}` : ''}</div>}
                            />
                            <Table.Column
                              title="单份价值(元)"
                              cell={(_, index, row) => (
                                <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                              )}
                            />
                          </Table>
                        </FormItem>
                      </FormItem>
                    </Form>
                  </Tab.Item>
                );
              })}
            </Tab>
          </FormItem>
        </FormItem>
        <FormItem label="前置订单时间">{`${format.formatDateTimeDayjs(
          formData.orderBeforeStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderBeforeEndTime)}`}</FormItem>
        <FormItem label="后置订单时间类型">
          {formData.limitOrderTimeType === 0
            ? '同活动时间'
            : `${format.formatDateTimeDayjs(formData.orderAfterStartTime)}至${format.formatDateTimeDayjs(
                formData.orderAfterEndTime,
              )}`}
        </FormItem>
        <FormItem label="订单状态">{formData.orderRestrainStatus === 1 ? '已完成' : '已付款'}</FormItem>
        <FormItem label="奖品延迟发放">{formData.awardDays > 0 ? `延迟发放${formData.awardDays}天` : '否'}</FormItem>

        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>

        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="阶段" dataIndex="period" />
              <Table.Column title="罐数" dataIndex="potNum" />
              <Table.Column title="商品id" dataIndex="skuId" />
              <Table.Column
                title="C端是否展示"
                cell={(data, index, row) => {
                  return <div>{row.isShow == 1 ? '是' : '否'}</div>;
                }}
              />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
