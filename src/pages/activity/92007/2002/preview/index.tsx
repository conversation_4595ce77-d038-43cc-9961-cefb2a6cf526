/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Input, Table } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import ChoosePromotion from '@/components/ChoosePromotion';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>

        <FormItem label="锁权时间">
          {formData.lockStartTime}-{formData.lockEndTime}
        </FormItem>
        <FormItem label="下单时间">
          {formData.orderStartTime}-{formData.orderEndTime}
        </FormItem>
        <FormItem label="领奖时间">
          {formData.distributeStartTime}-{formData.distributeEndTime}
        </FormItem>
        <FormItem label="锁权金额">{formData.lockAmount}元</FormItem>
        <FormItem label="活动商品">
          <Table dataSource={formData.giftSkuList} maxBodyHeight={400}>
            <Table.Column title="skuId" dataIndex="skuId" />
            <Table.Column title="系列名" dataIndex="seriesName" />
            <Table.Column title="罐数" dataIndex="specialSku" />
            <Table.Column title="排序" dataIndex="sortId" />
          </Table>
        </FormItem>

        <FormItem label="6罐奖品">{formData.sixPrizeOpen ? '开启' : '关闭'}</FormItem>
        {formData.sixPrizeOpen && (
          <FormItem label="6罐奖品设置" isPreview={false}>
            <Table dataSource={formData.sixPrizeList}>
              <Table.Column
                title="奖品名称"
                dataIndex="prizeName"
                cell={(_, index, row) => <div>{row.prizeName}</div>}
              />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
            </Table>
          </FormItem>
        )}

        <FormItem label="12罐奖品">{formData.twPrizeOpen ? '开启' : '关闭'}</FormItem>
        {formData.twPrizeOpen && (
          <FormItem label="12罐奖品设置" isPreview={false}>
            <Table dataSource={formData.twPrizeList}>
              <Table.Column
                title="奖品名称"
                dataIndex="prizeName"
                cell={(_, index, row) => <div>{row.prizeName}</div>}
              />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
            </Table>
          </FormItem>
        )}

        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
