/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:03
 * Description: 活动设置
 */
import React, { useImperativeHandle, useRef, useReducer, useState } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 弹幕设置
import BarrageSettings from './components/BarrageSettings';
// 奖品信息
import PrizesInfo from './components/Prizes';
// 活动限制
import LimitInfo from './components/Limit';
import { PageData, CustomValue } from '../util';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}
interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const onChange = (activityInfo, type?: string): void => {
    console.log('活动设置信息更新:', activityInfo);
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const barrageSettings = useRef<{ submit: () => void | null }>(null);
  const limitRef = useRef<{ submit: () => void | null }>(null);
  const prizesRef = useRef<{ submit: () => void | null }>(null);

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [baseRef.current!, barrageSettings.current, limitRef.current, prizesRef.current];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        const result = events[index].submit();
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  return (
    <div className={styles.setting}>
      <BaseInfo sRef={baseRef} {...settingProps} />
      <BarrageSettings sRef={barrageSettings} {...settingProps} />
      <PrizesInfo sRef={prizesRef} {...settingProps} />
      <LimitInfo sRef={limitRef} {...settingProps} />
    </div>
  );
};
