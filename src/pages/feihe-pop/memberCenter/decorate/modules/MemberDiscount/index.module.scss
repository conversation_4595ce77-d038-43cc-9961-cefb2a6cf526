$px-scale: 20;
$ui-scale: 1.5;

.preview {
  .member-discount-container {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    margin-bottom: 0.3px * $px-scale;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100.7% auto;
  }

  .member-discount-title {
    width: 100%;
    height: 20px;
    margin-top: 0.2px * $px-scale;
    margin-bottom: 15px;
    background-repeat: no-repeat;
    background-position: center left;
    background-size: auto 100%;
  }

  .link-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    padding: 0.6px * $px-scale;
    border: solid 1px #e5d1a9;
    border-radius: 0.5px * $px-scale;
    box-shadow: 0 5px 7px 2px rgba(110, 110, 110, 11%);
  }

  .link-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5px * $px-scale;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .link {
    &-length-1 {
      width: 100%;
      height: auto !important;
    }

    &-length-2 {
      width: 49%;
      height: auto !important;
    }

    &-length-3 {
      width: 31%;
      height: auto !important;
    }

    &-length-4 {
      width: 22.5%;
      height: auto !important;
    }
  }
}

.operation {
  .kvContainer {
    position: relative;
    margin-bottom: 10px;
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;

    .delete {
      position: absolute;
      top: 20px;
      right: 20px;
    }

    .imgUpload {
      display: flex;
      align-items: center;

      .tip {
        margin-left: 10px;
      }
    }
  }

  .itemContainer {
    display: flex;

    &:first-child {
      margin-top: 15px;
    }

    .itemImg {
      margin-right: 10px;
    }

    .itemInfo {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .icons {
        .transform180 {
          display: inline-block;
          transform: rotate(180deg);
        }

        i {
          margin-right: 5px;
          transform: rotate(180deg);
          cursor: pointer;
        }
      }
    }
  }
}
