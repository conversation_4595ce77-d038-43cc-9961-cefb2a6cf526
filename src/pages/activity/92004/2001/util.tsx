/**
 * Author: z<PERSON>yue
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  type: number;
  peopleNum: number;
  rank: string;
}

export interface CustomValue {
  actBg: string;
  actBgColor: string;
  ruleBtn: string;
  myPrizeBtn: string;
  rankPrize: string;
  rankBg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
}

export interface PageData {
  shopName: string;
  // 活动名称
  activityName: string;
  // 日期区间（不提交）
  rangeDate: Dayjs[];
  // 活动开始时间
  startTime: string;
  // 活动结束时间
  endTime: string;
  // 活动门槛（不提交）
  threshold: number;
  // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
  supportLevels: string;
  crowdPackage: number;
  prizeSendType: number;
  // 奖品列表
  prizeList: PrizeInfo[];
  giftSkuList: any[];
  // 订单商品类型 0 全店 1 指定
  orderSkuType: number;
  orderSkuList: any[];

  // 是否限制每人每天中奖次数 1 不限制 2 限制
  winLotteryDayType: number;
  // 每人每天中奖次数
  winLotteryDayCounts: number;
  // 是否限制每人累计次数 1 不限制 2 限制
  winLotteryTotalType: number;
  // 每人累计中奖次数
  winLotteryTotalCounts: number;
  // 分享标题
  shareStatus: number;
  shareTitle: string;
  // 分享图片
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  // 活动规则
  rules: string;
  templateCode: string;
  gradeLabel: string[];
  crowd: any;
  crowdBag: any;

  isRank: number;
  evaluateRankTime: any[];
  statisticsStartTime: string;
  statisticsEndTime: string;
  rankTime: Dayjs[];
  rankStartTime: string;
  rankEndTime: string;
  prizeRankList: any[];
  sharePrizeList: any[];
  endActivity: number;

  limitJoinTimeType: number;
  // 入会时间
  joinTimeRange: any[];
  joinStartTime: string;
  joinEndTime: string;
  isInvite: number;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '令牌',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/276114/4/21639/84645/68009ca4F47ee0dff/e8b5135cea83ad9a.png',
  actBgColor: '#e6e4e9',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/279162/31/18263/1717/67f7af74F616737c9/aace214116988f93.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/271135/32/21242/1766/67fda59cFe0e57d05/51a21b2a7ecbcb32.png',
  rankPrize: 'https://img10.360buyimg.com/imgzone/jfs/t1/273365/22/21842/64217/68009ca3F96622e24/bed2d567c7a120b7.png',
  // 排行榜背景
  rankBg: '//img10.360buyimg.com/imgzone/jfs/t1/276025/9/22109/12420/68009ca2Ffa2a5b1d/338d253f25fcb0aa.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/94488/9/27154/41467/6361dc07E7c95b556/9025d95fd750384d.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/94488/9/27154/41467/6361dc07E7c95b556/9025d95fd750384d.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/94488/9/27154/41467/6361dc07E7c95b556/9025d95fd750384d.png',
};
// 活动设置默认数据
export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  type: 0, // 0门槛奖品 1排行榜奖品
  peopleNum: 1,
  rank: '', // 获奖名次 默认0
};
export const PRIZE_INFO_RANK: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  type: 1, // 0门槛奖品 1排行榜奖品
  peopleNum: 0,
  rank: '', // 获奖名次 默认0
};
export const INIT_PAGE_DATA = (): PageData => {
  return {
    shopName: getShop().shopName,
    crowd: undefined,
    // 活动名称
    activityName: `邀请入会有礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 活动结束生成人群包
    crowdPackage: 1,
    prizeSendType: 1, // 奖项类型 1 单一型 2 jie'ti'xing
    // 奖品列表
    prizeList: [],
    giftSkuList: [],
    // 订单商品类型 0 全店 1 指定
    orderSkuType: 0,
    orderSkuList: [],
    // 是否限制每人每天中奖次数 1 不限制 2 限制
    winLotteryDayType: 1,
    // 每人每天中奖次数
    winLotteryDayCounts: 1,
    // 是否限制每人累计次数 1 不限制 2 限制
    winLotteryTotalType: 1,
    // 每人累计中奖次数
    winLotteryTotalCounts: 1,
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '开卡领奖，更有排行大奖，给我助力，一起开卡领奖 ！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    isRank: 1, // 排行榜 0/false-不开启 1/true-开启
    evaluateRankTime: [dayjs().add(0, 'day'), null],
    statisticsStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    statisticsEndTime: '',
    rankTime: [], // 排行榜领奖时间
    rankStartTime: '',
    rankEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    endActivity: 0,
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    sharePrizeList: [
      {
        ...PRIZE_INFO,
        rank: '',
        id: `id-${Math.random().toString(36).substr(2, 9)}`,
        sortId: 1,
      },
    ],
    prizeRankList: [
      {
        ...PRIZE_INFO_RANK,
        rank: '',
        id: `id-${Math.random().toString(36).substr(2, 9)}`,
        sortId: 1,
      },
    ],
    isInvite: 2,
  };
};

// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
export const generateMembershipStringRule = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `指定人群`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  const newPrizeList = prizeList.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 0);
  if (!newPrizeList.length) {
    Message.error('请设置邀请有礼奖品');
    return false;
  }
  const newPrize1 = prizeList.filter((e: Pick<PrizeInfo, 'prizeType'>): boolean => e.prizeType <= 0 || !e.prizeType);
  if (newPrize1.length > 0) {
    Message.error('请设置邀请有礼奖品');
    return false;
  }
  const noPeopleNumArr = newPrizeList.filter((e) => !e.peopleNum);
  if (noPeopleNumArr && noPeopleNumArr.length > 0) {
    Message.error('邀请入会人数不能为空');
    return false;
  }
  const noPeopleNumArr1 = newPrizeList.filter((e) => e.peopleNum <= 0);
  if (noPeopleNumArr1 && noPeopleNumArr1.length > 0) {
    Message.error('邀请入会人数不能为0');
    return false;
  }
  for (let i = 1; i < newPrizeList.length; i++) {
    if (newPrizeList[i].peopleNum <= newPrizeList[i - 1].peopleNum) {
      Message.error('邀请人数必须递增');
      return false;
    }
  }
  return true;
};

const winCounts = (formData: PageData): boolean => {
  if (formData.prizeSendType === 1) {
    if (formData.winLotteryDayType === 2) {
      if (formData.winLotteryDayCounts > formData.prizeList[0].sendTotalCount) {
        Message.error('每人每天最多中奖次数不能大于奖品发放份数');
        return false;
      }
    }
  }
  if (
    formData.winLotteryTotalType === 2 &&
    formData.winLotteryDayType === 2 &&
    formData.winLotteryDayCounts > formData.winLotteryTotalCounts
  ) {
    Message.error('每日中奖次数不能大于累计中奖次数');
    return false;
  }
  return true;
};
const mergePrizeListByType = (prizeList: any) => {
  const newPrizeList: PrizeInfo[] = [];
  const prizeMap: Record<string, PrizeInfo> = {};
  prizeList.forEach((prize) => {
    const key = `${prize.prizeType}-${prize.prizeKey}`;
    if (prizeMap[key]) {
      prizeMap[key].sendTotalCount += prize.sendTotalCount;
    } else {
      prizeMap[key] = { ...prize };
      newPrizeList.push(prizeMap[key]);
    }
  });
  return newPrizeList;
};

// 校验排行榜时间
const rankCheck = (formData: PageData) => {
  let allPrize: any = [];
  if (formData.isRank === 1) {
    if (!formData.statisticsStartTime) {
      Message.error('请设置排行榜统计开始时间');
      return false;
    }
    if (!formData.statisticsEndTime) {
      Message.error('请设置排行榜统计结束时间');
      return false;
    }
    if (dayjs(formData.statisticsStartTime).isBefore(dayjs(formData.startTime))) {
      Message.error('排行榜统计开始时间应大于活动开始时间');
      return false;
    }
    if (dayjs(formData.statisticsEndTime).isAfter(dayjs(formData.endTime))) {
      Message.error('排行榜统计结束时间应小于活动结束时间');
      return false;
    }

    if (dayjs(formData.statisticsStartTime).isAfter(dayjs(formData.statisticsEndTime))) {
      Message.error('排行榜统计开始时间应小于排行榜统计结束时间');
      return false;
    }
    if (dayjs(formData.statisticsStartTime).isSame(dayjs(formData.statisticsEndTime))) {
      Message.error('排行榜统计开始时间不能等于排行榜统计结束时间');
      return false;
    }
    if (!formData.rankEndTime) {
      Message.error('请设置排行榜奖励发放结束时间');
      return false;
    }
    if (dayjs(formData.rankEndTime).isAfter(dayjs(formData.endTime))) {
      Message.error('排行榜奖励发放结束时间应小于活动结束时间');
      return false;
    }
    if (dayjs(formData.rankEndTime).isSame(dayjs(formData.rankStartTime))) {
      Message.error('排行榜奖励发放开始时间不能等于排行榜奖励发放结束时间');
      return false;
    }
    if (!formData.prizeRankList || formData.prizeRankList.length === 0) {
      Message.error('请设置排行榜奖品');
      return false;
    }

    const newPrizeRank = formData.prizeRankList.filter(
      (e: Pick<PrizeInfo, 'prizeType'>): boolean => e.prizeType <= 0 || !e.prizeType,
    );

    if (newPrizeRank.length > 0) {
      Message.error('请设置排行榜奖品');
      return false;
    }
    allPrize = formData.prizeList.concat(formData.prizeRankList);
  } else {
    allPrize = formData.prizeList;
  }
  // if (mergePrizeListByType(allPrize)) {
  //   // quantityAvailable 和sendTotalCount比较
  //   const newPrizeList: any = mergePrizeListByType(allPrize);
  //   const obj = {
  //     3: 'quantityAvailable',
  //     12: 'quantityRemain',
  //     2: 'quantityRemain',
  //     6: 'quantityRemain',
  //     7: 'cardSurplus',
  //     8: 'quantityRemain',
  //     9: 'quantityRemain',
  //     10: 'quantityRemain',
  //   };
  //   // for (let i = 0; i < newPrizeList.length; i++) {
  //   //   if (newPrizeList[i].sendTotalCount > newPrizeList[i].quantityAvailable) {
  //   //     Message.error(`${PRIZE_TYPE[newPrizeList[i].prizeType]}奖品总发放份数不能大于奖品库存`);
  //   //     return false;
  //   //   }
  //   // }
  //   // console.log(newPrizeList, 'result==========');
  // }
  return true;
};
export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  // 非编辑模式校验
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (formData.isRank === 0 && formData.isInvite === 1) {
    Message.error('请至少配置一种领奖方式（邀请有礼奖品/邀请排榜奖品）');
    return false;
  }
  // 没有选择奖品
  if (formData.isInvite === 2 && !hasPrize(prizeList)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (formData.isInvite === 2 && !arePrizesValid(prizeList, formData)) {
    return false;
  }
  // 中奖次数限制
  if (formData.isInvite === 2 && !winCounts(formData)) {
    return false;
  }
  if (!rankCheck(formData)) {
    return false;
  }
  return true;
};
