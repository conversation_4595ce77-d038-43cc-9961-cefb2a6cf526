import { savePageJson, savePageJsonDraft } from '@/api/swisse';
import LzImageSelector from '@/components/LzImageSelector';
import { Button, Card, Divider, Form, Input, Message, Radio } from "@alifd/next";
import React from 'react';
import styles from './index.module.scss';
import { checkUrl } from "@/pages/mengniu/utils";
import { deepCopy } from "@/utils";

const RadioGroup = Radio.Group;

function TimeLimitedFlash({ data, dispatch, allJson, defaultData }) {
  const pageinfo = defaultData.filter((item) => item.pageType == data.pageType);
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };

  const [pageLoading, setPageLoading] = React.useState(false);
  React.useEffect(() => {}, []);

  const publish = (): any => {
    if (!data.content.background.imgUrl) {
      Message.error('请上传背景图');
      return false;
    }
    if (!data.content.activityUrl.imgUrl) {
      Message.error('请上传E卡活动图');
      return false;
    }
    if (!data.content.activityUrl.linkUrl) {
      Message.error('活动链接不能为空');
      return false;
    }
    if (!data.hour) {
      Message.error('时间不能为空');
      return false;
    }

    const update = deepCopy(data);
    delete update.tokenAct;

    const updateJson = allJson.filter((item) => item.pageType != data.pageType);
    updateJson.push(update);
    savePageJson({ pageInfo: JSON.stringify(updateJson) })
      .then((res) => {
        Message.success('发布成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };
  const validateNavUrl = (rule, value, callback) => {
    if (!value) {
      callback(`链接不能为空`);
    } else {
      try {
        if (checkUrl(value)) {
          callback(`链接必须以 jd.com 或 isvjcloud.com 结尾`);
        } else {
          callback();
        }
      } catch (error) {
        callback(`请输入有效的 URL`);
      }
    }
  };
  const saveSetting = (): any => {
    if (!data.content.background.imgUrl) {
      Message.error('请上传图片');
      return false;
    }
    const updateJson = allJson.filter((item) => item.pageType != data.pageType);
    updateJson.push(data);
    console.log('updateJson', updateJson);
    setPageLoading(true);
    savePageJsonDraft({ pageInfo: JSON.stringify(updateJson) })
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };

  const commonProps = {
    title: data.pageName,
    extra: (
      <div>
        <RadioGroup
          value={data.showType}
          disabled
          onChange={(showType) => {
            data.showType = showType;
            dispatch({ type: 'UPDATE_MODULE', payload: data.showType });
          }}
        >
          <Radio id="5" value={5}>
            新客限时秒杀
          </Radio>
          <Radio id="0" value={0}>
            全显示
          </Radio>
          <Radio id="1" value={1}>
            限新客
          </Radio>
          <Radio id="2" value={2}>
            限老客
          </Radio>
          <Radio id="3" value={3}>
            不显示
          </Radio>
        </RadioGroup>
        <Button type="primary" onClick={publish} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">展示时间</div>
              <div style={{ display: `flex`, alignItems: `center` }}>
                新客入会后
                <Input
                  value={data.hour}
                  style={{ margin: `10px` }}
                  type="number"
                  placeholder=""
                  onChange={(value) => {
                    if (Number(value) > 100) {
                      return Message.error('请小于100小时');
                    }
                    const numericRegex = /^[0-9]*$/;
                    const newValueIsNumeric = numericRegex.test(value);

                    if (newValueIsNumeric) {
                      data.hour = value;
                      setData(data);
                    } else {
                      data.hour = pageinfo[0].hour;
                      setData(data);
                      return Message.error('请输入数字');
                    }
                  }}
                />
                小时内可见
              </div>
              <div className="crm-label">背景图</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={data.content.background.width}
                  height={data.content.background.height}
                  value={data.content.background.imgUrl}
                  onChange={(img) => {
                    data.content.background.imgUrl = img;
                    setData(data);
                  }}
                />
                <div className={styles.tip}>
                  <div>
                    图片尺寸：仅支持宽度为{data.content.background.width}px，高度{data.content.background.height}
                    px
                  </div>
                  <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                </div>
              </div>
              <div className={styles.colorPicker}>
                <Button
                  style={{ marginLeft: '35px' }}
                  type={'primary'}
                  text
                  onClick={() => {
                    data.content.background.imgUrl = pageinfo[0].content.background.imgUrl;
                    setData(data);
                  }}
                >
                  重置
                </Button>
              </div>
              <div className="crm-label">E卡活动图</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={data.content.activityUrl.width}
                  height={data.content.activityUrl.height}
                  value={data.content.activityUrl.imgUrl}
                  onChange={(img) => {
                    data.content.activityUrl.imgUrl = img;
                    setData(data);
                  }}
                />
                <div className={styles.tip}>
                  <div>
                    图片尺寸：仅支持宽度为{data.content.activityUrl.width}px，高度{data.content.activityUrl.height}
                    px
                  </div>
                  <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                </div>
              </div>
              <Form.Item
                name="adLink"
                label="E卡活动跳转链接"
                validator={(rule, value, callback) => validateNavUrl(rule, value, callback)}
              >
                <Input
                  value={data.content.activityUrl.linkUrl}
                  trim
                  placeholder="请输入跳转链接"
                  onChange={(value) => {
                    data.content.activityUrl.linkUrl = value;
                    setData(data);
                  }}
                />
              </Form.Item>
              <div className={styles.colorPicker}>
                <Button
                  style={{ marginLeft: '35px' }}
                  type={'primary'}
                  text
                  onClick={() => {
                    data.content.activityUrl.imgUrl = pageinfo[0].content.activityUrl.imgUrl;
                    data.content.activityUrl.linkUrl = pageinfo[0].content.activityUrl.linkUrl;
                    setData(data);
                  }}
                >
                  重置
                </Button>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}

export default TimeLimitedFlash;
