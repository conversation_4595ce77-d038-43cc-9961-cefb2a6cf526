/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, NumberPicker, Input, Grid, Message } from '@alifd/next';

import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import { activityEditDisabled, deepCopy, isDisableSetPrize } from '@/utils';
import { FormLayout, SeriesList, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';
import styles from '@/pages/activity/90007/1001/setting/style.module.scss';

interface Props {
  value: SeriesList | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<SeriesList>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  // 暂存累计/连续签到天数
  const [signDaySto, setSignDaySto] = useState('');
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    if (formData.seriesPrizeList.length >= 2 && editValue?.prizeKey !== data.prizeKey) {
      const itemPrizeKey = data.prizeKey;
      const isFind = formData.seriesPrizeList.find((item) => item.prizeKey === itemPrizeKey);
      if (isFind) {
        Message.error('同系列奖品不可重复设置');
        return;
      }
    }
    // 更新指定index 奖品信息
    if (tableName === 'prizeDay') {
      formData.prizeDay[target] = data;
    } else if (tableName === 'seriesPrizeList') {
      formData.seriesPrizeList[target] = { ...data, potNum: signDaySto };
    }
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.seriesPrizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.seriesPrizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 1 - prizeListLength; i++) {
      list.push(deepCopy(PRIZE_INFO));
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({ seriesPrizeList: list.length ? list : formData.seriesPrizeList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      console.log(898);
      let err: object | null = null;
      field.validate((errors): void => {
        console.log('errors', errors);
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel title="商品及奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem required label="系列名">
            <FormItem>
              <Input
                disabled
                value={formData.seriesName}
                placeholder="请输入系列名称"
                name="activityName"
                maxLength={20}
                showLimitHint
                className="w-300"
              />
            </FormItem>
          </FormItem>
          <FormItem required requiredMessage={'请上传系列图'} label="系列图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={559}
                  height={240}
                  value={formData.seriesPic}
                  onChange={(seriesPic) => {
                    setData({ seriesPic });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tips}>
                  <p>图片尺寸：559px*240px</p>
                  <p>图片大小：不超过32KB</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    disabled={activityEditDisabled()}
                    type="primary"
                    text
                    onClick={() => {
                      setData({ seriesPic: '' });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
            <Input className="validateInput" name="seriesPic" value={formData.seriesPic} />
          </FormItem>
          <FormItem required requiredMessage={'请输入链接'} label="链接">
            <Input
              disabled={activityEditDisabled()}
              value={formData.seriesUrl}
              placeholder="请输入链接"
              name="seriesUrl"
              className="w-300"
              onChange={(seriesUrl) => {
                setData({ seriesUrl });
              }}
            />
          </FormItem>
          <FormItem label="SKU列表">
            <Button
              onClick={() => {
                setSeriesSkuList(formData.seriesSkuList);
                setSkuVisible(true);
              }}
            >
              查看SKU
            </Button>
          </FormItem>
          <FormItem required label="奖品列表">
            <FormItem>
              <Table dataSource={formData.seriesPrizeList} style={{ marginTop: '15px' }}>
                <Table.Column
                  title="罐数门槛"
                  cell={(_, index, row) => (
                    <FormItem required requiredMessage="请输入罐数门槛">
                      <NumberPicker
                        disabled={activityEditDisabled()}
                        min={1}
                        max={9999999}
                        step={1}
                        type="inline"
                        value={row.potNum}
                        onChange={(potNum: number) => {
                          formData.seriesPrizeList[index].potNum = potNum;
                          setData(formData);
                        }}
                        name={`potNum-${index}`}
                      />
                    </FormItem>
                  )}
                />
                <Table.Column title="奖品名称" dataIndex="prizeName" />
                <Table.Column
                  title="奖品类型"
                  cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                  dataIndex="prizeType"
                />
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    if (row.prizeType === 1) {
                      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                    } else {
                      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                    }
                  }}
                />
                <Table.Column
                  title="发放份数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="单份价值(元)"
                  cell={(_, index, row) => (
                    <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                  )}
                />
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <FormItem disabled={isDisableSetPrize(formData.seriesPrizeList, index)}>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            setSignDaySto(formData.seriesPrizeList[index].potNum);
                            let row = formData.seriesPrizeList[index];
                            if (row.prizeName === '') {
                              row = null;
                            }
                            setEditValue(row);
                            setTarget(index);
                            setTableName('seriesPrizeList');
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        {formData.seriesPrizeList.length > 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认删除该奖品？',
                                onOk: () => {
                                  formData.seriesPrizeList.splice(index, 1);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        )}
                      </FormItem>
                    )}
                  />
                )}
              </Table>
            </FormItem>
            {!activityEditDisabled() && (
              <FormItem>
                <Button
                  disabled={formData.seriesPrizeList.length >= 6}
                  type="primary"
                  onClick={() => {
                    formData.seriesPrizeList.push(deepCopy(PRIZE_INFO));
                    setData(formData);
                  }}
                >
                  +添加奖品（{formData.seriesPrizeList.length}/6）
                </Button>
              </FormItem>
            )}
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
          typeList={[3]}
          defaultTarget={3}
          prizeNameLength={12}
        />
      </LzDialog>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="	罐数" dataIndex="potNum" />
              <Table.Column title="商品id" dataIndex="skuId" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
