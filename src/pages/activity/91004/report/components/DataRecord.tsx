import React, { useEffect, useState } from 'react';
import { Form, DatePicker2, Field, Table, Button, Balloon, Icon } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataDzReport, dataDzReportExport } from '@/api/v91004';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ];
  const titleCell = (title: string, content: string) => {
    return (
      <Balloon.Tooltip
        v2
        trigger={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <span dangerouslySetInnerHTML={{ __html: title }} />
            <Icon style={{ cursor: 'pointer', marginLeft: '5px' }} type="help" size="small" />
          </div>
        }
        align="tl"
      >
        {content}
      </Balloon.Tooltip>
    );
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.dateRange = query.dateRange.map((item) => dayJs(item).format('YYYY-MM-DD'));
    dataDzReport(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    formValue.dateRange = formValue.dateRange.map((item) => dayJs(item).format('YYYY-MM-DD'));
    console.log(formValue, 'formValue');
    dataDzReportExport(formValue).then((data: any) => downloadExcel(data, `定制报表`));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="查询时间">
          <RangePicker
            // showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={'YYYY-MM-DD'}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table.StickyLock dataSource={tableData} loading={loading}>
        <Table.Column width={100} dataIndex={'day'} title={'日期'} lock="left" />

        <Table.ColumnGroup title={<div style={{ textAlign: 'center' }}>人数</div>}>
          <Table.Column width={100} dataIndex={'pv'} title={titleCell('pv', '活动期间的累计来访PV')} />
          <Table.Column width={100} dataIndex={'uv'} title={titleCell('uv', '活动期间的累计来访UV，不去重')} />
          <Table.Column
            width={100}
            dataIndex={'noneMember'}
            title={titleCell('非会员', '活动期间的累计来访UV中非会员')}
          />
          <Table.Column width={100} dataIndex={'member'} title={titleCell('会员', '活动期间的累计来访UV中会员')} />
          <Table.Column
            width={150}
            dataIndex={'oldMember'}
            title={titleCell('会员老客', '活动期间的累计来访UV中会员，且活动开始时间前店铺已购')}
          />
          <Table.Column
            width={150}
            dataIndex={'newMember'}
            title={titleCell('会员新客', '活动期间的累计来访UV中会员，且活动开始时间前店铺未购，且活动期间首购')}
          />
          <Table.Column
            dataIndex={'newMemberOldJoin'}
            width={150}
            title={titleCell('会员新客-老入会', '会员新客，且活动开始前入会')}
          />
          <Table.Column
            dataIndex={'newMemberNewJoin'}
            width={150}
            title={titleCell('会员新客-新入会', '会员新客，且活动期间入会')}
          />
        </Table.ColumnGroup>

        <Table.ColumnGroup title={<div style={{ textAlign: 'center' }}>金额</div>}>
          <Table.Column
            width={120}
            dataIndex={'newMemberJoinAmount'}
            title={titleCell('会员新客', '报名后，且活动开始时间前店铺未购，且活动期间首购')}
          />
          <Table.Column
            width={150}
            dataIndex={'newMemberOldJoinAmount'}
            title={titleCell('会员新客-老入会', '会员新客，且活动开始前入会')}
          />
          <Table.Column
            width={150}
            dataIndex={'newMemberNewJoinAmount'}
            title={titleCell('会员新客-新入会', '会员新客，且活动期间入会')}
          />
        </Table.ColumnGroup>

        <Table.ColumnGroup title={<div style={{ textAlign: 'center' }}>会员新客-老入会-浏览活动前首购</div>}>
          <Table.Column
            width={120}
            dataIndex={'prizeWinners'}
            title={titleCell('领奖人数', '会员新客首购发生在浏览活动之前，且领奖的用户数')}
          />
          <Table.Column
            width={150}
            dataIndex={'prizeAmountPaid'}
            title={titleCell('领奖金额', '会员新客首购发生在浏览活动之前，且领奖的人数用户购买金额')}
          />
          <Table.Column
            width={120}
            dataIndex={'nonPrizeWinners'}
            title={titleCell('未领奖人数', '会员新客首购发生在浏览活动之前，且未领奖的用户数')}
          />
          <Table.Column width={120} dataIndex={'nonPaid'} title={titleCell('报名人数', '报名人数')} />
        </Table.ColumnGroup>

        <Table.ColumnGroup title={<div style={{ textAlign: 'center' }}>会员新客-老入会-浏览活动后首购</div>}>
          <Table.Column
            width={120}
            dataIndex={'prizeOldWinners'}
            title={titleCell('领奖人数', '会员新客首购发生在浏览活动之后，且领奖的用户数')}
          />
          <Table.Column
            width={150}
            dataIndex={'prizeOldAmount'}
            title={titleCell('领奖金额', '会员新客首购发生在浏览活动之后，且领奖的人数用户购买金额')}
          />
          <Table.Column
            width={120}
            dataIndex={'nonPrizeOldWinners'}
            title={titleCell('未领奖人数', '会员新客首购发生在浏览活动之后，且未领奖的用户数')}
          />
          <Table.Column width={120} dataIndex={'nonOldAmount'} title={titleCell('报名人数', '报名人数')} />
        </Table.ColumnGroup>

        <Table.ColumnGroup title={<div style={{ textAlign: 'center' }}>会员新客-新入会-浏览活动前首购</div>}>
          <Table.Column
            width={120}
            dataIndex={'prizeWinnersNewMemberNewJoinBefore'}
            title={titleCell('领奖人数', '会员新客首购发生在浏览活动之前，且领奖的用户数')}
          />
          <Table.Column
            width={150}
            dataIndex={'prizeAmountPaidNewMemberNewJoinBefore'}
            title={titleCell('领奖金额', '会员新客首购发生在浏览活动之前，且领奖的人数用户购买金额')}
          />
          <Table.Column
            width={120}
            dataIndex={'nonPrizeWinnersNewMemberNewJoinBefore'}
            title={titleCell('未领奖人数', '会员新客首购发生在浏览活动之前，且未领奖的用户数')}
          />
          <Table.Column width={120} dataIndex={'nonNewMemberNewJoinBefore'} title={titleCell('报名人数', '报名人数')} />
        </Table.ColumnGroup>

        <Table.ColumnGroup title={<div style={{ textAlign: 'center' }}>会员新客-新入会-浏览活动前首购</div>}>
          <Table.Column
            width={120}
            dataIndex={'prizeWinnersNewMemberNewJoinAfter'}
            title={titleCell('领奖人数', '会员新客首购发生在浏览活动之后，且领奖的用户数')}
          />
          <Table.Column
            width={150}
            dataIndex={'prizeAmountPaidNewMemberNewJoinAfter'}
            title={titleCell('领奖金额', '会员新客首购发生在浏览活动之后，且领奖的人数用户购买金额')}
          />
          <Table.Column
            width={120}
            dataIndex={'nonPrizeWinnersNewMemberNewJoinAfter'}
            title={titleCell('未领奖人数', '会员新客首购发生在浏览活动之后，且未领奖的用户数')}
          />
          <Table.Column width={120} dataIndex={'nonNewMemberNewJoinAfter'} title={titleCell('报名人数', '报名人数')} />
        </Table.ColumnGroup>
      </Table.StickyLock>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
