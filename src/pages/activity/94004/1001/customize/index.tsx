/**
 * Author: wa<PERSON><PERSON>
 * Date: 2024-07-19
 * Description: 设置模板
 */
import React, { useEffect, useState } from 'react';
import { CustomValue } from '../util';
// 主页
import Base from './components/Base';
// 成功页
import Success from './components/Success';
// 不满足页
import Failed from './components/Failed';
// 不满足所属门店页
import NonsupportShop from './components/NonsupportShop';
import { Tab } from '@alifd/next';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  handleChangeActiveKey: (activeKey: string) => void;
  handleLevelActiveKey: (levelActiveKey: string) => void;
  isScreen: boolean;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { defaultValue, value, handleChange } = props;
  const [activeKey, setActiveKey] = useState('1');
  const handleTabChange = (data) => {
    setActiveKey(data);
    props.handleChangeActiveKey(data);
  };
  // 判断是否正在截图
  useEffect(() => {
    if (props.isScreen) {
      handleTabChange('1');
    }
  }, [props.isScreen]);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };

  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab activeKey={activeKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Tab.Item title="活动主页" key="1" />
        <Tab.Item title="申领成功页" key="2" />
        <Tab.Item title="不满足页" key="3" />
        <Tab.Item title="所属门店不满足页" key="4" />
      </Tab>
      {activeKey === '1' && <Base handleLevelActiveKey={props.handleLevelActiveKey} {...eventProps} />}
      {activeKey === '2' && <Success handleLevelActiveKey={props.handleLevelActiveKey} {...eventProps} />}
      {activeKey === '3' && <Failed handleLevelActiveKey={props.handleLevelActiveKey} {...eventProps} />}
      {activeKey === '4' && <NonsupportShop {...eventProps} />}
    </div>
  );
};
