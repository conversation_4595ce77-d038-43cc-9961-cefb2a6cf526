import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Dialog, Field, Form, Input, Message, Rating, Table } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataGiveLog, dataGiveLogExport } from '@/api/v96007';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import styles from '@/pages/activity/96007/1001/preview/style.module.scss';
import { FormLayout } from '@/pages/activity/96007/1001/util';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataGiveLog(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataGiveLogExport(formValue).then((data: any) => downloadExcel(data, `领奖机会赠送记录`));
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  // 详情弹框
  const [formData, setFormData] = useState<any>({});
  // 评价弹框
  const [visible, setVisible] = useState(false);
  // 图片弹框
  const [visibleImg, setVisibleImg] = useState(false);
  // 图片弹框url
  const [visibleImgUrl, setVisibleImgUrl] = useState('');
  const onOpen = (data) => {
    setFormData(data);
    setVisible(true);
  };
  const onClose = () => {
    setVisible(false);
  };

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="dateRange" label="评价时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="评价时间"
          dataIndex="evaluateTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.evaluateTime)}</div>}
        />
        <Table.Column title="SkuId" dataIndex="skuId" />
        <Table.Column title="过滤状态" dataIndex="filterStatus" />
        <Table.Column title="赠送领奖机会" dataIndex="giveNum" />
        <Table.Column title="备注" dataIndex="remark" />
        <Table.Column
          title="评价内容"
          dataIndex="content"
          cell={(value, index, data) => (
            <div>
              <Button type={'primary'} text onClick={() => onOpen(data)}>
                详情
              </Button>
            </div>
          )}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />

      {/* 评价内容弹框 */}
      <Dialog v2 visible={visible} width="700px" onOk={onClose} onClose={onClose} footer={false}>
        <div className={styles.preview}>
          <Form {...formItemLayout}>
            <FormItem label="昵称" required>
              <span style={{ verticalAlign: 'middle', lineHeight: '28px' }}> {formData.nickName}</span>
            </FormItem>
            <FormItem label="头像" required>
              <img style={{ width: '100px', height: '100px' }} src={formData.headerImg} alt="" />
            </FormItem>
            <FormItem label="评价内容" required>
              <span style={{ verticalAlign: 'middle', lineHeight: '28px' }}> {formData.content}</span>
            </FormItem>
            <FormItem label="评价等级" required>
              <div>
                <Rating defaultValue={formData.score} disabled size="large" />
              </div>
            </FormItem>
            <FormItem label="评价图片" required>
              {formData.skuimage &&
                Array.isArray(JSON.parse(formData.skuimage)) &&
                JSON.parse(formData.skuimage).map((item, index) => {
                  return (
                    <img
                      style={{ paddingLeft: '10px', width: '100px', height: '100px', objectFit: 'contain' }}
                      key={index}
                      src={`http://img10.360buyimg.com/n0/${item.imgUrl}`}
                      alt=""
                      onClick={() => {
                        setVisibleImg(true);
                        setVisibleImgUrl(`http://img10.360buyimg.com/n0/${item.imgUrl}`);
                      }}
                    />
                  );
                })}
            </FormItem>
            <FormItem label="评价视频" required>
              {formData.videos &&
                Array.isArray(JSON.parse(formData.videos)) &&
                JSON.parse(formData.videos).map((item, index) => {
                  return (
                    <video
                      style={{
                        paddingLeft: '10px',
                        width: '300px',
                        maxHeight: '475px',
                      }}
                      key={index}
                      controls
                      src={item.videoUrl}
                    />
                  );
                })}
            </FormItem>
          </Form>
        </div>
      </Dialog>
      <Dialog visible={visibleImg} footer={false} onClose={() => setVisibleImg(false)}>
        <img src={visibleImgUrl} alt="" style={{ width: '600px', height: '600px', objectFit: 'contain' }} />
      </Dialog>
    </div>
  );
};
