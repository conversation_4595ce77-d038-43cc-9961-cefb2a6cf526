import React, { useReducer, useEffect, useImperativeHandle, useState } from 'react';
import { Form, Field, Input, Radio, Grid, Message, NumberPicker, Balloon, Button } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import ChoosePromotion from '@/components/ChoosePromotion';
import { activityEditDisabled, getParams } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import ChooseGoods from '@/components/ChooseGoods';
import styles from '../../style.module.scss';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

class Sku {
  jdPrice: string;
  seq: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
  constructor(skuId: number) {
    this.jdPrice = '0';
    this.seq = 0;
    this.skuId = skuId;
    this.skuMainPicture =
      '//img10.360buyimg.com/imgzone/jfs/t1/44645/27/24207/24564/659d00d9F0467f8c5/ef658035745cc059.png';
    this.skuName = '钻光夜乳12ml';
  }
}

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [inputSkuList, setInputSkuList] = useState('');
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  // const [visible, setVisible] = useState(false);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const Disabled = () => {
    return activityEditDisabled() || getParams('type') === 'edit';
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    if (data) {
      formData.prizeList[0] = data;
    } else {
      formData.prizeList = [];
    }
    setData(formData);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect(() => {
    const skuIds = formData.giftSkuList.map((item: any) => item.skuId);
    const str = skuIds.join(',');
    setInputSkuList(str);
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  // 订单商品相关
  const handleOrderSkuChange = (data) => {
    setData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };
  const removeOrderSku = (index: number) => (): void => {
    formData.orderSkuList.splice(index, 1);
    setData({ orderSkuList: formData.orderSkuList });
  };
  // 曝光商品相关
  const handleSkuChange = (data) => {
    setData({ skuList: data });
    field.setErrors({ skuList: '' });
  };

  // 处理用户输入SKU
  const checkInputSku = (val) => {
    const regex = /^[0-9,]*$/;
    // 使用正则表达式进行验证
    if (!regex.test(val)) {
      Message.error('只允许输入数字及英文逗号');
      return;
    }
    if (val.match(/,,/)) {
      Message.error('禁止连续输入两个及以上的逗号');
      return;
    }
    const skuArr = val.split(',');
    // 检查是否有超过 20 位数字的 SKU
    const isSkuTooLong = skuArr.some((skuId) => skuId.length > 15);
    if (isSkuTooLong) {
      Message.error('SKU 限制为 15 位');
      return;
    }
    if (skuArr.length > 10) {
      Message.error('最多添加10个SKU');
      return;
    }
    const giftSkuList: Sku[] = [];
    skuArr.forEach((skuId) => {
      if (skuId) {
        giftSkuList.push(new Sku(parseInt(skuId, 10)));
      }
      // if (skuId) {
      //   try {
      //     const bigIntSkuId = skuId.toString();
      //     giftSkuList.push(new Sku(bigIntSkuId));
      //   } catch (e) {
      //     Message.error(`无效的 SKU: ${skuId}`);
      //   }
      // }
    });
    setInputSkuList(val);
    setData({ giftSkuList });
  };

  // 修改限制金额
  const changeFullGiftThreshold = (val) => {
    setData({
      fullGiftThreshold: val.toString(),
    });
  };
  // 修改参与人数
  const changeMaxParticipateNum = (val) => {
    setData({
      maxParticipateNum: val.toString(),
    });
  };
  return (
    <div>
      <LzPanel
        title="奖品设置"
        actions={
          <Button
            type="primary"
            text
            onClick={() => {
              window.open('https://www.yuque.com/luzekeji/bfc3uk/zm9hot5lmex11zc2');
            }}
          >
            令牌使用攻略
          </Button>
        }
      >
        <Form {...formItemLayout} field={field}>
          <FormItem isPreview label="奖品类型">
            总价促销令牌（满赠）
          </FormItem>
          <FormItem label="添加令牌" required>
            <ChoosePromotion
              promoType={'10'}
              submit={onPrizeChange}
              value={formData.prizeList[0] ?? null}
              disabled={Disabled()}
            />
            <div className="next-form-item-help">
              注：请商家注意，已关联活动的令牌，尽量谨慎在资产中心手工添加用户，避免活动奖品不足的风险。
            </div>
          </FormItem>
          <Form.Item label="发放时机" isPreview>
            <div>
              用户在活动首页，满足活动参与门槛及参与规则，点击“立即申请”后，系统自动发放总价促销令牌（即系统自动将用户加入令牌中）
            </div>
          </Form.Item>
          <Form.Item label="赠品SKU" required requiredMessage="请输入赠品SKU">
            <Input.TextArea
              name="rules"
              value={inputSkuList}
              placeholder="请输入令牌所绑定的总价促销活动的赠品SKU，多个以英文逗号分隔"
              maxLength={170}
              style={{ width: '300px' }}
              showLimitHint
              hasBorder
              rows={8}
              className="form-input-ctrl"
              disabled={Disabled()}
              onChange={checkInputSku}
            />
            <div className={styles.tip}>
              1. 添加令牌所绑定的总价促销活动的赠品SKU，最多添加10个；
              <div>
                2. <span style={{ color: 'red' }}>不要填写影子分身SKU，否则会造成用户刷单。</span>
              </div>
            </div>
          </Form.Item>
          <Form.Item label="累计最大参与人数" required requiredMessage="请输入累计最大参与人数">
            <NumberPicker
              className={styles.formNumberPicker}
              name="maxParticipateNum"
              value={formData.maxParticipateNum}
              onChange={changeMaxParticipateNum}
              type="inline"
              min={1}
              max={9999999}
              disabled={Disabled()}
            />
            <div className={styles.tip}>
              <div>1. 达到人数上限后，用户将不可参与此促销活动</div>
              <div>2. 请根据累计最大参与人数预留赠品，请保证赠品库存充足；</div>
            </div>
          </Form.Item>
          <FormItem label="满赠门槛" required requiredMessage="请输入满赠门槛">
            <NumberPicker
              className={styles.formNumberPicker}
              type="inline"
              min={1}
              max={9999999}
              name="fullGiftThreshold"
              disabled={Disabled()}
              value={formData.fullGiftThreshold}
              onChange={changeFullGiftThreshold}
            />
            元
            <div className={styles.tip}>
              务必与官方后台设置信息一致，满赠门槛会在活动规则、移动端展示{' '}
              <Balloon
                v2
                trigger={
                  <Button text type={'primary'}>
                    查看官方设置示例
                  </Button>
                }
                triggerType="hover"
                closable={false}
                popupClassName={styles.balloon}
              >
                <div>
                  <img
                    style={{ width: '550px' }}
                    src="//img10.360buyimg.com/imgzone/jfs/t1/96612/28/48228/18239/655ac235F12454c02/81cabf0d490d2b6d.png"
                    alt=""
                  />
                </div>
              </Balloon>
            </div>
          </FormItem>
          {/* <FormItem label="参与活动正装商品(即主商品)" required>
            <RadioGroup
              disabled={Disabled()}
              value={formData.orderSkuType}
              onChange={(orderSkuType: number) => {
                if (!orderSkuType) {
                  formData.orderSkuList = [];
                }
                setData({ orderSkuType, orderSkuList: formData.orderSkuList });
              }}
            >
              <Radio id="0" value={0}>
                全店正装商品
              </Radio>
              <Radio id="1" value={1}>
                指定商品
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {formData.orderSkuType === 1 && (
                <FormItem name="orderSkuList" required requiredMessage={'请选择指定商品'} style={{ marginTop: '15px' }}>
                  <ChooseGoods disabled={Disabled()} value={formData.orderSkuList} onChange={handleOrderSkuChange} />
                  <div className={styles.container}>
                    {formData.orderSkuList?.map((sku, index) => {
                      return (
                        <div key={sku.skuId} className={styles.skuContainer}>
                          {!Disabled && (
                            <i
                              onClick={removeOrderSku(index)}
                              className={['iconfont', 'icon-iconww-37', styles.del].join(' ')}
                            />
                          )}
                          <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
                          <div>
                            <div className={styles.skuName}>{sku.skuName}</div>
                            <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                            <div className={styles.price}>¥ {sku.jdPrice}</div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList} />
                </FormItem>
              )}
            </Grid.Row>
            <div className="next-form-item-help">
              <div>此部分商品填写的是申请令牌时关联的商品，不在C端展示，是为用户加入令牌和移除令牌（使用户只</div>
              <div>
                能参加一次活动）提供依据，请仔核对令牌促销活动中的商品信息，
                <span style={{ color: 'red' }}>此处所填写参与活动的正装商品</span>
              </div>
              <div style={{ color: 'red' }}>必须和令牌关联的商品保持一致，填错将会有客诉风险</div>
            </div>
          </FormItem> */}
          <FormItem label="曝光商品" style={{ marginTop: '15px' }}>
            <ChooseGoods disabled={Disabled()} value={formData.skuList} onChange={handleSkuChange} max={20} />
            <div className="next-form-item-help">
              <div>此商品应为令牌所绑定的总价促销活动的主商品（即用户参加总价促销活动可以购买的正装商品）</div>
              <div>添加后将在移动端展示，请仔细核对，若商品填写错误，意味着用户购买后不会有赠品，将会有客诉风险</div>
            </div>
            <Input className="validateInput" name="skuList" value={formData.skuList.toString()} />
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
