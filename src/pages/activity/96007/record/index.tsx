import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import WinRecord from '@/pages/activity/96007/record/components/WinRecord';
import ChanceGiftRecord from '@/pages/activity/96007/record/components/ChanceGiftRecord';
import { dataLotteryLogExport, dataWinningLogExport, dataGiveLogExport } from '@/api/v96007';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="中奖记录" key="1">
            <WinRecord />
          </Tab.Item>
          <Tab.Item title="领奖机会赠送记录" key="2">
            <ChanceGiftRecord />
          </Tab.Item>
        </Tab>
    </div>
  );
};
