/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 14:58
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect, useState, useRef } from 'react';
import { Form, Field, Radio, DatePicker2, Table, Button, Dialog, NumberPicker, Input, Grid } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { activityEditDisabled, getShopOrderStartTime } from '@/utils';
import { FormLayout, PageData, PRIZE_INFO, PRIZE_TYPE } from '../../../util';
import BosidengChooseGoods from '@/components/BosidengChooseGoods';
import BosidengSkuImport from '@/components/BosidengSkuImport';
import styles from './index.module.scss';
import LzTipPanel from '@/components/LzTipPanel';
import format from '@/utils/format';
import constant from '@/utils/constant';
import dayjs from 'dayjs';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import LzToolTip from '@/components/LzToolTip';

const { RangePicker } = DatePicker2;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const BosidengChooseGoodsRef = useRef<{ submit: () => void | null }>(null);
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();

  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 选择奖品
  const [visible, setVisible] = useState(false);

  const [plan, setPlan] = useState<any[]>([]);
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // const handleSkuChange = (data) => {
  //   setData({ orderSkuList: data });
  //   field.setErrors({ orderSkuList: '' });
  // };
  const handleSkuChange = (data) => {
    // const nerSkuList = deepCopy(formData.orderSkuList);
    setData({ orderSkuList: [...data] });
    console.log('前二十曝光商品信息', [...data]);
    field.setErrors({ orderSkuList: '' });
  };
  // 剩余SKU
  const handleSaveSkus = (skuObj) => {
    const lastObj = { ...formData.surplusSkuObj, ...skuObj };
    for (const key in lastObj) {
      if (lastObj.hasOwnProperty(key) && lastObj[key] === '') {
        // 如果是，则删除该属性
        delete lastObj[key];
      }
    }
    setData({ surplusSkuObj: lastObj });
  };

  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: 0,
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRangeData');
    }, 1000);
  }, [formData.endTime]);
  const [maxDays, setMaxDays] = React.useState(180);
  const [firstIn, setFirstIn] = React.useState(false);
  useEffect(() => {
    if (!firstIn) {
      setFirstIn(true);
      return;
    }
    let diff = 180;
    if (shopOrderInfo.longTermOrder) {
      diff = dayjs(formData.endTime).diff(dayjs(shopOrderInfo.shopOrderStartTime), 'day');
    } else {
      diff = shopOrderInfo.orderRetentionDays;
    }
    setMaxDays(diff);
    if (diff < formData.days) {
      setData({ days: diff });
    }
  }, [formData.startTime, shopOrderInfo.shopOrderStartTime]);
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRangeData): void => {
    setData({
      orderRangeData,
      orderStartTime: format.formatDateTimeDayjs(orderRangeData[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRangeData[1]),
    });
  };
  const addPrize = () => {
    formData.prizeList.push(PRIZE_INFO);
    setData({ prizeList: formData.prizeList });
    field.setErrors({ prizeList: '' });
  };

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;
      field.validate((errors): void => {
        err = errors;
      });
      const res = BosidengChooseGoodsRef.current?.submit();
      if (!err) {
        err = res;
      }
      return err;
    },
  }));

  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    if (data.prizeType === 1) {
      // 后端用于发奖和保存校验需要
      data.prizeKey = target.toString();
    }
    // 更新指定index 奖品信息
    formData.prizeList[target] = { ...formData.prizeList[target], ...data };
    const list = formData.prizeList.map((item) => item.prizeKey);
    setPlan(list);
    setData(formData);
    setVisible(false);
  };

  useEffect(() => {
    // 根据奖品列表长度判断是否设置过奖品
    const prizeLength = formData.prizeList.length;
    if (!prizeLength) {
      const prizeList = new Array(2).fill(PRIZE_INFO);
      setData({ prizeList });
    }
  }, []);

  const renderStepAmount = (v, index, record) => {
    const clonedPrizeList = JSON.parse(JSON.stringify(formData.prizeList));
    return (
      <div>
        累计金额（元）
        <Form.Item name={`prize-${index}`} style={{ marginTop: '5px' }} required requiredMessage="请填写累计金额">
          <NumberPicker
            disabled={activityEditDisabled()}
            style={{ margin: '0 5px' }}
            value={record.stepAmount}
            onChange={(stepAmount) => {
              clonedPrizeList[index].stepAmount = stepAmount;
              setData({ prizeList: clonedPrizeList });
            }}
            type="inline"
            min={1}
            precision={2}
            size={'small'}
            step={1}
            name={`prize-${index}`}
            max={9999999}
            className={styles.number}
          />
        </Form.Item>
      </div>
    );
  };
  return (
    <div>
      <LzPanel title="参与规则">
        <LzTipPanel message="提示：用户订单满足以下条件后获得领奖资格，领取奖励后，待订单完成，系统将自动发放奖励；领取上限为1份/人" />
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="下单时间" required requiredMessage="请选择下单时间">
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.orderRangeData}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
              }}
            />
            <div className={styles.tip}>
              注：1、默认支持查询
              {shopOrderInfo.longTermOrder
                ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
              的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
              <br />
              2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。
            </div>
          </FormItem>
          <FormItem label="订单拆单后是否合并" required>
            <RadioGroup
              value={formData.split}
              onChange={(split: number) => {
                setData({ split });
              }}
            >
              <Radio id="1" value={1}>
                是
              </Radio>
              <Radio id="0" value={0}>
                否
              </Radio>
            </RadioGroup>
            <LzToolTip
              content={
                <div>
                  <div>
                    <p>拆单：因平台因素，用户的某笔总的父订单会有被拆单成多笔子订单发货的情况。</p>
                    <br />
                    当用户订单在平台上被拆分为多笔子订单时，可以选择以下方式来判断活动参与门槛： <br />
                    • 选择“是”：按照父订单（将本店铺的子订单合并后的订单）来判断参与门槛。 <br />
                    • 选择“否”：按照拆单后的子订单（包括父订单）来判断参与门槛。 <br />
                  </div>
                </div>
              }
            />
          </FormItem>
          <FormItem label="价格类型" required>
            <RadioGroup
              value={formData.priceType}
              onChange={(priceType: number) => {
                setData({ priceType });
              }}
            >
              <Radio id="0" value={0}>
                京东价
                <LzToolTip
                  content={
                    <div>
                      <div>选择【全部商品】时，使用订单金额计算，即整笔订单京东价；</div>
                      <div>选择【指定商品】时，使用活动指定商品的 SKU京东价 × 购买数量 计算；</div>
                      <div> SKU京东价，为京东后台创建商品时所设置的SKU京东价；</div>
                    </div>
                  }
                />
              </Radio>
              <Radio id="1" value={1}>
                实付价 (POP店为实付价，自营店为京东价)
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="订单商品" required>
            <RadioGroup
              value={formData.isAllOrderSku}
              onChange={(isAllOrderSku: number) => {
                setData({
                  isAllOrderSku,
                  orderSkuList: !isAllOrderSku ? formData.orderSkuList : [],
                });
              }}
            >
              <Radio id="1" value={1}>
                全部商品
              </Radio>
              <Radio id="0" value={0}>
                指定商品
                <LzToolTip content="商家需配置指定SKU为参与活动商品。满足参与资格的用户需在下单时间段内下单指定商品，满足XX元，且订单已完成，才有机会获得奖励；需注意：计算金额时，使用活动指定商品的SKU京东价×购买数量计算，而不是包含活动指定商品的整笔订单金额计算" />
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {formData.isAllOrderSku === 0 && (
                <FormItem name="orderSkuList" required requiredMessage={'请选择订单商品'} style={{ marginTop: '15px' }}>
                  <div>前20个sku：</div>
                  <BosidengChooseGoods
                    value={formData.orderSkuList}
                    onChange={handleSkuChange}
                    max={500}
                    isOrder
                    sRef={BosidengChooseGoodsRef}
                  />
                  <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList} />
                </FormItem>
              )}
            </Grid.Row>
            <Grid.Row>
              {formData.isAllOrderSku === 0 && (
                <FormItem
                  name="surplusSkuObj"
                  // required
                  // requiredMessage={'请选择剩余订单商品'}
                  style={{ marginTop: '15px' }}
                >
                  <div>剩余SKU：</div>
                  <BosidengSkuImport handleSaveSkus={handleSaveSkus} surplusSkuObj={formData.surplusSkuObj} isOrder />
                  <p className={styles.tip}>
                    注：优先按店铺顺序，再按照skuid填入的顺序，展示前20个上传的sku；剩余商品随机排序
                  </p>
                  <Input className="validateInput" name="surplusSkuObj" value={formData.surplusSkuObj} />
                </FormItem>
              )}
            </Grid.Row>
          </FormItem>
          <FormItem label="新老客限制" required>
            <RadioGroup
              value={formData.customerLimit}
              onChange={(customerLimit: number) => {
                field.setErrors({ customerLimitDay: '' });
                setData({ customerLimit, customerLimitDay: 1 });
              }}
            >
              <Radio id="0" value={0}>
                不限制
              </Radio>
              <Radio id="1" value={1}>
                仅限新客参与
                <LzToolTip content="输入X天前到下单开始时间段内不存在订单完成状态的用户。" />
              </Radio>
              <Radio id="2" value={2}>
                仅限老客参与
                <LzToolTip content="输入X天前到下单开始时间段内存在订单完成状态的用户。" />
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {formData.customerLimit === 1 && (
                <FormItem
                  name="customerLimitDay"
                  style={{ marginTop: 10 }}
                  required
                  requiredMessage={'请输入新客限制天数'}
                >
                  近
                  <NumberPicker
                    min={1}
                    max={maxDays}
                    type="inline"
                    name="customerLimitDay"
                    style={{ margin: '0 5px' }}
                    value={formData.customerLimitDay}
                    onChange={(customerLimitDay) => {
                      setData({ customerLimitDay });
                    }}
                  />
                  天内未购买
                </FormItem>
              )}
            </Grid.Row>

            <Grid.Row>
              {formData.customerLimit === 2 && (
                <FormItem
                  style={{ marginTop: 10 }}
                  name="customerLimitDay"
                  required
                  requiredMessage={'请输入老客限制天数'}
                >
                  近
                  <NumberPicker
                    min={1}
                    max={maxDays}
                    type="inline"
                    name="customerLimitDay"
                    style={{ margin: '0 5px' }}
                    value={formData.customerLimitDay}
                    onChange={(customerLimitDay) => {
                      setData({ customerLimitDay });
                    }}
                  />
                  天内已购
                </FormItem>
              )}
            </Grid.Row>
          </FormItem>
          <FormItem label="设置阶梯奖励" required requiredMessage={'请设置阶梯奖励'}>
            <Button disabled={formData.prizeList.length >= 5} onClick={addPrize}>
              添加
            </Button>
            <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
              <Table.Column title="阶梯等级" cell={(v, index) => <div>{index + 1}级</div>} />
              <Table.Column
                title={() => (
                  <div>
                    金额
                    <LzToolTip
                      content={
                        <div>
                          <div>选择【全部商品】时，使用订单金额计算，即整笔订单京东价;</div>
                          <div>选择【指定商品】时，使用活动指定商品的 SKU京东价 × 购买数量 计算；</div>
                          <div> SKU京东价，为京东后台创建商品时所设置的SKU京东价</div>
                          <div> 实付价(POP店为实付价，自营店为京东价)</div>
                        </div>
                      }
                    />
                  </div>
                )}
                cell={renderStepAmount}
                dataIndex="stepAmount"
              />
              <Table.Column title="奖品名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(v, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }}
              />
              <Table.Column
                title="单位价值(元)"
                cell={(v, index, row) => <div>{row.unitPrice ?? Number(row.unitPrice).toFixed(2)}</div>}
              />
              <Table.Column
                title="发放份数"
                cell={(v, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(v, index, row) => <img style={{ width: '20px' }} src={row.prizeImg} alt="" />}
              />
              {!activityEditDisabled() && (
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, r) => (
                    <div>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.prizeList[index];
                          if (row.prizeName === '谢谢参与') {
                            row = null;
                          }
                          setEditValue(row);
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          Dialog.confirm({
                            v2: true,
                            title: '提示',
                            centered: true,
                            content: '确认清空阶梯奖励？',
                            onOk: () => {
                              formData.prizeList.splice(index, 1, PRIZE_INFO);
                              setData(formData);
                            },
                            onCancel: () => console.log('cancel'),
                          } as any);
                        }}
                      >
                        清空
                      </Button>
                      <Button
                        text
                        type="primary"
                        disabled={formData.prizeList.length <= 2}
                        onClick={() => {
                          Dialog.confirm({
                            v2: true,
                            title: '提示',
                            centered: true,
                            content: '确认删除该阶梯奖励？',
                            onOk: () => {
                              formData.prizeList.splice(index, 1);
                              setData(formData);
                            },
                            onCancel: () => console.log('cancel'),
                          } as any);
                        }}
                      >
                        删除
                      </Button>
                    </div>
                  )}
                />
              )}
            </Table>
            <Input className="validateInput" name="prizeList" value={formData.prizeList.length ? 1 : ''} />
          </FormItem>
          <FormItem label="领取限制" required>
            <RadioGroup
              value={formData.receiveLimit}
              onChange={(receiveLimit: number) => {
                setData({ receiveLimit });
              }}
            >
              <Radio id="1" value={1}>
                单次领取
                <LzToolTip content="单次领取，指用户满足多个阶梯层级时，只能选择一个阶梯层级领取奖励，不可领取全部奖励" />
              </Radio>
              <Radio id="2" value={2}>
                多次领取
                <LzToolTip content="多次领取，指用户满足多个阶梯层级时，可以领取全部奖励" />
              </Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </LzPanel>
      <LzDialog
        title={false}
        style={{ width: '670px' }}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
      >
        <ChoosePrize
          formData={formData}
          hasProbability={false}
          hasLimit={false}
          editValue={editValue}
          onChange={onPrizeChange}
          planList={plan}
          onCancel={() => setVisible(false)}
          isBosideng
          defaultTarget={2}
          typeList={[1, 2, 3, 4, 6, 7]}
        />
      </LzDialog>
    </div>
  );
};
