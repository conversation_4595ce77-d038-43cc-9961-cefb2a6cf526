import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  potNum: string;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  prizeKey: string;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface seriesSkuList {
  skuId: string;
  tankNum: number;
}

export interface SeriesList {
  seriesName: string;
  stepList: StepList[];
}

export interface StepList {
  stepName: string;
  tankNum: string;
  prizeList: PrizeInfo[];
}

export interface CustomValue {
  pageBg: string;
  actBgColor: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;

  dialogBg: string;
  titleTextColor: string;
  otherTextColor: string;
  highlightTextColor: string;
  btnTextColor: string;
  btnBgColor: string;
  highlightBtnTextColor: string;
  highlightBtnBgColor: string;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  shareStatus: number;
  shareTitle: string;
  rules: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  selectedOptions: any[];
  moduleInfo: object;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置基础信息', '② 活动设置', '③ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/282117/4/5955/195682/67dbca95Fef5fdb70/523bb0fcd6f5ad10.jpg',
  actBgColor: '#f02816', // 主页背景色
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/279026/32/9061/41157/67e1284dF767f99ad/ecf68271ad90ef6d.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/279142/30/9023/5263/67e1284eF8311ac7b/713b3a7cfbacc716.jpg',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274190/1/9056/48480/67e1284eF7dfff0a8/0aa0802faa26ce5c.jpg',

  dialogBg: '//img10.360buyimg.com/imgzone/jfs/t1/292402/30/8709/7840/68301533Fb22798dd/505712491aa20be6.png',
  titleTextColor: '#fff',
  otherTextColor: '#fff',
  highlightTextColor:'#f9c104',
  btnTextColor:'#fff',
  btnBgColor:'#15005d',
  highlightBtnTextColor:'#fff',
  highlightBtnBgColor:'#ca0e22',
};

export const INIT_PAGE_DATA = (): PageData => {
  const now = (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00');
  const after30 = (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59');
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `骁龙能量站-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [now, after30],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(now),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(after30),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '骁龙能量站，超多惊喜大奖等你来领！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: "规则文案",
    selectedOptions: ["kvModule"],

    moduleInfo: {
      selectedModule: 'kvModule',
      modules: {
        kvModule: {
          name: 'kv模块',
          pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/317049/23/1935/36509/682acfa2F65410986/034f08414c4b6119.png',
          ruleText: '活动结束后，所有未消耗能量值，将按照10:1的比例，自动兑换为骁龙会员积分，积分将在活动结束后3个工作日内发放到账',
          memberPoint: 1,
          energyValue: 10,
          ruleTextColor: '#a4041e',
          ruleBtnColor: '#fff',
          memberTextColor: '#fff',
          memberBtnColor: '#15005d',
          otherTextColor: '#fff',
        },
        signUpModule: {
          name: '签到模块',
          pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/296982/32/8511/10780/682ad2d9F527cc1d3/6b8e09b66298f082.png',
          hasSignUpIconBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/292300/22/7728/5673/682acfd1F39263cab/92b06219d9ffbddf.png',
          noSignUpIconBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/304225/29/3297/4294/682acfd1F6a6bf7fb/2c1cd050916e5c31.png',
          noSignUpIconDateBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/313152/30/2176/5602/682ad401F305cd4ee/02b343c66060ea24.png',
          leftArrowBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/290828/22/7536/3675/682acfd0F1b203ab0/324c5d0ae6076e83.png',
          rightArrowBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/287373/26/8125/3669/682acfd0Fb4df8710/32203d33fcbd662c.png',
          signUpBtnColor: '#15005d',
          signUpTextColor: '#fff',
          signUpRecordBtnColor: '#fff',
          signUpRecordTextColor: '#a4041e',
          signUpEnergyTextColor: '#000',
          otherTextColor: '#fff',
          highlightTextColor: '#e9b37d',
          prizeList: [],
        },
        answerModule: {
          name: '答题模块',
          answerPageBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/300619/23/8062/21063/682c341dFaaece74c/f0d29b756eab5c3c.png',
          answeringPageBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/299285/6/8744/9756/682c341dF68dc784e/9c4037d38d615031.png',
          challengeBtnColor: '#e71324', //开始挑战按钮
          challengeTextColor: '#fff', //开始挑战文案

          dialogBtnColor: '#15005d', // 答题记录按钮
          dialogTextColor: '#fff', // 答题记录文案
          // 选项文字颜色
          optionTextColor: '#fff',
          // 选项按钮颜色
          optionBtnColor: '#15005d',
          // 选中选项按钮颜色
          optionBtnSelectedColor: '#fff',
          // 选中选项字体颜色
          optionTextSelectedColor: '#15005d',
          // 继续答题按钮颜色
          continueBtnColor: '#15005d',
          // 继续答题文字颜色
          continueTextColor: '#fff',
          // 高亮文字颜色
          highlightTextColor: '#e9b37d',
          // 普通文字颜色
          otherTextColor: "#fff",
          pageType:1,
          questionsList:[],
          prizeList:[],
          ruleText:'',
          fileName: '',
          fileList: [],
        },
        hotZoneModule: {
          name: '热区模块',
          hotZoneSetting: {
            bg: '//img10.360buyimg.com/imgzone/jfs/t1/317049/23/1935/36509/682acfa2F65410986/034f08414c4b6119.png',
            hotZoneList: [],
          },
        },
        prizeDrawModule: {
          name: '抽奖模块',
          pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/314020/18/2879/9664/682d91bcFe3197354/fd7fba476b96c94a.png',
          btnBg: '//img10.360buyimg.com/imgzone/jfs/t1/308161/31/2830/8954/682d91bcF70b828bb/ea9e2bf9cc221972.png',
          giftTextColor: '#fff',
          dialogBtnColor: '#15005d', // 答题记录按钮
          dialogTextColor: '#fff', // 答题记录文案

          prizeList: [],
          energyValue: 50,
          // 是否限制每人每天中奖次数 1 不限制 2 限制
          winLotteryDayType: 1,
          // 每人每天中奖次数
          winLotteryDayCounts: 1,
          // 是否限制每人累计次数 1 不限制 2 限制
          winLotteryTotalType: 1,
          // 每人累计中奖次数
          winLotteryTotalCounts: 1,
          ruleText:''
        },
        swiperModule: {
          name: '轮播模块',
          pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/304879/25/3673/9443/682dc1cbF2a6ca031/a8d770c64e1ee12b.png',
          swiperList: [ ],
        },
        hotProductModule: {
          name: '商品模块',
          pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/300668/2/7531/13731/682ed246Fc3172dd9/1a591adb90941149.png',
          btnBg: '//img10.360buyimg.com/imgzone/jfs/t1/286512/31/6332/4411/682ed464F81617f5a/8f9fd45488cbf1cd.png',
          tabBg: '//img10.360buyimg.com/imgzone/jfs/t1/298187/11/9322/4120/682e9ca2Fe0202e2c/4defebd5e4137bbe.png',
          tabActiveBg:
            '//img10.360buyimg.com/imgzone/jfs/t1/316157/29/3067/3640/682e9ca2Fc240b4e9/2a82bb9d8f328f2f.png',
          // 是否展示按钮 1 不限制 2 限制
          btnShowType: 1,
          btnList: [],
          productList: [],
          // 高亮文字颜色
          highlightTextColor: '#e9b37d',
          // 普通文字颜色
          otherTextColor: '#fff',
          fileName: '',
          fileList: [],
        },
        flipCardModule: {
          name: '翻牌模块',
          pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/317718/12/3133/8739/683013c1Fdab40e65/943da1da4c4a1ee9.png',
          cardBg: '//img10.360buyimg.com/imgzone/jfs/t1/286456/39/6513/4173/683013c0F6ca00556/853bd44eb556e405.png',
          flipBtn: '//img10.360buyimg.com/imgzone/jfs/t1/315246/29/3240/3264/68301456F36c9fffc/8b9c47b303b5e02a.png',
          dialogBtnColor: '#15005d', // 弹窗按钮颜色
          dialogTextColor: '#fff', // 弹窗按钮文字颜色
          // 规则文字颜色
          ruleTextColor: '#fff',
          // 翻卡次数文字颜色
          flipCountTextColor: '#fff',
          ruleText: '奖励一：翻开3张不同卡片，获得60能量\n' +
            '奖励二：翻开全套卡片，可获得500能量,\n' +
          '达成目标，奖励自动发放，两个奖励每个ID活动期仅可各获得一次。',
          // 卡片数量
          cardNumType: 4,
          flipCount: '3',
          // 能量值奖励机制列表
          energyValueList: [
            {
              id: 1,
              // 翻卡次数
              cardNum: '',
              // 能量值
              energyValue: '',
            },
          ],
          cardList: [
            {
              id: 0,
              // 正面图片
              front: '',
              // 反面图片
              back: '',
              // 卡片名称
              name: '',
              // 概率
              probability: '',
            },
            {
              id: 1,
              front: '',
              back: '',
              name: '',
              probability: '',
            },
            {
              id: 2,
              front: '',
              back: '',
              name: '',
              probability: '',
            },
            {
              id: 3,
              front: '',
              back: '',
              name: '',
              probability: '',
            },
          ],
        },
        wishModule: {
          name: "许愿模块",
          pageBg: "//img10.360buyimg.com/imgzone/jfs/t1/300650/28/10221/16148/68352548F683f6f97/6799f34a2f8f075f.png",

          otherColor: "#fff",
          dialogBtnColor: "#15005d", // 答题记录按钮
          dialogTextColor: "#fff", // 答题记录文案

          wishesBtnColor: "#15005d",
          wishesTextColor: "#fff",

          prizeWishBtnColor:"#ffdb34",
          prizeWishTextColor:"#000",

          addBtnColor: "#fff",
          addTextColor: "#000",

          highlightTextColor: "#f9c104",

          prizeList:[],
          energyValue:50,
          wishCounts:3,
          ruleText:'消耗能量选择心仪的礼品进行许愿，每次许愿消耗X能量值。\n' +
            '每份礼品活动期内最多许愿3次，多次提交愿望可提高中奖概率。\n' +
            '中奖结果将在X月X日公布，可在公布后点击我的许愿记录查询。',
          wishEndTime:'',
          showStartTime:''
        },
        cardCollectingModule: {
          name: "集片模块",
          pageBg: "//img10.360buyimg.com/imgzone/jfs/t1/287960/26/10503/39341/68354fa8Fe0559dc5/e08048618a4fdc28.png",
          dialogBtnColor: "#15005d", // 答题记录按钮
          dialogTextColor: "#fff", // 答题记录文案
          cardDrawBtnColor: "#e71324",
          cardDrawTextColor: "#fff",
          cardBtnColor:"#15005d",
          cardTextColor:"#fff",
          ruleTextColor: "#fff",
          otherColor: "#fff",
          cardList:[
            {
              img: "//img10.360buyimg.com/imgzone/jfs/t1/319787/24/4124/14154/68356022F7690ff74/c37579ae97aa1cf2.png",
              cardName: "稀有卡",
              cardType: 1,
              probability:5,
            }
          ],
          ruleText:'每个用户每日有3次抽卡机会，点击我的卡片可查看已拥有的卡片，消耗一定的卡片数量即可兑换能量奖励',
          cardCountLimitDay:3, // 每人每日抽卡次数
          useSingleCount:1, // 消耗不同卡片张数
          cardEnergyValueOfCards:50,//兑换能量（不同卡片）
          exchangeCountsOfCard:1, // 兑换次数（不同卡片）
          cardEnergyValueOfAllCards:300,//兑换能量（全套）
          exchangeCountsOfAllCard:1, // 兑换次数（不同卡片）
        },
        comSortList: [
          "kvModule",
        ]
      },
      defaultModule: {},
      pushAllowed: {
        kvModule: 0,
        signUpModule: 0,
        hotZoneModule: 0,
        answerModule: 0,
        prizeDrawModule: 0,
        hotProductModule: 0,
        swiperModule: 0,
        wishModule: 0,
        cardCollectingModule: 0,
        flipCardModule: 0,
      }
    }
  };
};
export const moduleOptions = [
  {
    des: 'kvModule',
    title: 'kv模块',
  },
  {
    des: 'signUpModule',
    title: '签到模块',
  },
  {
    des: 'answerModule',
    title: '答题模块',
  },
  {
    des: 'prizeDrawModule',
    title: '抽奖模块',
  },
  {
    des: 'wishModule',
    title: '许愿模块',
  },
  {
    des: 'cardCollectingModule',
    title: '集卡模块',
  },
  {
    des: 'flipCardModule',
    title: '翻卡模块',
  },
  {
    des: 'swiperModule',
    title: '轮播模块',
  },
  {
    des: 'hotZoneModule',
    title: '热区模块',
  },
  {
    des: 'hotProductModule',
    title: '商品模块',
  },

];
export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  potNum: '',
  ifPlan: 0,
  prizeKey: '',
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType() && +getParams('status') === 2) {
    return true;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  return true;
};
