import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import LzPanel from '@/components/LzPanel';
import { PageData, EvaluateSkuList } from '@/pages/activity/96007/1001/util';
import { Button, Dialog, Message, Upload } from '@alifd/next';
import { downloadExcel } from '@/utils';
import { skuTemplateExport } from '@/api/v96007';
import { config } from 'ice';
import CONST from '@/utils/constant';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [fileList, setFileList] = useState<File[]>([]);
  // const [uploaderRef, setUploaderRef] = useState(false);
  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    // setUploaderRef(ref.getInstance());
  };

  const downloadTemplate = async () => {
    try {
      const data: any = await skuTemplateExport();
      downloadExcel(data, '批量导入sku模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  const prizesRef: any[] = [];
  useImperativeHandle(sRef, (): { submit: () => object | null } => ({
    submit: () => {
      console.log(1213213);
      console.log(prizesRef);
      let err: object | null = null;
      for (let i = 0; i < prizesRef.length; i++) {
        const e = prizesRef[i].submit();
        if (e) {
          err = e;
        }
      }
      return err;
    },
  }));
  return (
    <LzPanel title="评价商品设置">
      <div>
        <Message type="notice" style={{ marginBottom: 10 }}>
          导入须知： <br />
          1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
          <br />
          2.单次导入最大5M，导入中请不要关闭此页面。
          <br />
          <Button text type="primary" onClick={downloadTemplate}>
            下载模板
          </Button>
        </Message>
        <Upload
          action={`${config.baseURL}/96007/importSkuExcel`}
          name="file"
          method="post"
          headers={{
            token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
            prd: localStorage.getItem(CONST.LZ_SSO_PRD),
          }}
          ref={saveUploaderRef}
          value={fileList}
          limit={1}
          listType="text"
          accept=".xls,.xlsx"
          onChange={(info) => {
            if (info.length) {
              if (info[0].size > 5 * 1024 * 1024) {
                Message.error('文件大小不能超过5M');
                return;
              }
            } else {
              // 当文件列表为空时（用户删除文件），清空evaluateSkuList
              setData({ evaluateSkuList: [] });
            }
            prizesRef.splice(0);
            setFileList(info);
          }}
          onError={(res) => {
            if (res.state === 'error') {
              if (res.response?.message) {
                Message.error(res.response?.message);
              } else {
                Message.error('文件错误，请上传正确的文件');
              }
            }
          }}
          onSuccess={(res) => {
            console.log(res);
            if (res.response.code === 200) {
              console.log(res, '上传数据');
              // setTemporarySeriesList(res.response.data);
              const evaluateSkuList: EvaluateSkuList[] = [];
              res.response.data.forEach((item) => {
                evaluateSkuList.push({
                  skuId: item.skuId,
                  skuMainPicture: item.skuMainPicture,
                  skuName: item.skuName,
                  imgType: item.imgType,
                  nameType: item.nameType,
                });
              });
              setData({ evaluateSkuList });
              Dialog.success({
                title: '导入结果',
                content: (
                  <div>
                    <p>导入成功</p>
                  </div>
                ),
                onOk: () => {
                  console.log('导入成功');
                },
              });
            } else if (res.response?.message) {
              setFileList([]);
              Message.error(res.response?.message);
            } else {
              setFileList([]);
              Message.error('文件错误，请上传正确的文件');
            }
          }}
          style={{ marginBottom: 10 }}
        >
          <div className="next-upload-drag">
            <p className="next-upload-drag-icon">
              <Button type="primary">
                上传sku数据
              </Button>
            </p>
            <p className="next-upload-drag-hint">支持xls类型的文件</p>
          </div>
        </Upload>
      </div>
      {formData.evaluateSkuList && formData.evaluateSkuList.length > 0 && (
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '16px',
          marginTop: '16px'
        }}>

          {formData.evaluateSkuList.map((item, index) => (
            <div key={index} style={{
              width: 'calc(33.333% - 11px)',
              minWidth: '280px',
              border: '1px solid #e6e6e6',
              borderRadius: '8px',
              padding: '12px',
              backgroundColor: '#fafafa',
              display: 'flex',
              flexDirection: 'column',
              gap: '8px'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px'
              }}>
                <img
                  src={item.skuMainPicture}
                  alt=""
                  style={{
                    width: '60px',
                    objectFit: 'contain',
                    borderRadius: '4px',
                    border: '1px solid #ddd'
                  }}
                />
                <div style={{ flex: 1 }}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#333',
                    marginBottom: '4px',
                    lineHeight: '1.4'
                  }}>
                    {item.skuName}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#666',
                    fontFamily: 'monospace'
                  }}>
                    skuId：{item.skuId}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </LzPanel>
  );
};
