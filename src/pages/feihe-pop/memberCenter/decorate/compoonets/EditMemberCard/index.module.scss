.editMemberCard {
  position: relative;
  padding-bottom: 30px;


  .imgUpload {
    display: flex;
    align-items: flex-start;
    .tip {
      margin-left: 10px;
    }
  }
  .levelHeader {
    display: flex;
    justify-content: space-between;

    .label {
      display: flex;
      align-items: flex-start;
      span {
        margin-left: 5px;
        font-size: 10px;
        color: gray;
      }
    }

  }
  .levelContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .levelEquity {
      position: relative;

      .delBtn {
        position: absolute;
        top: 0;
        right: 0;
        background: #efefef;
        padding: 3px;
        cursor: pointer;

        i {
          font-size: 12px;
        }
      }
    }

    :global {
      .next-form-item-help {
        width: 192px;
      }
    }

    .levelUrl {
      width: 192px;
      margin-top: 10px;
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }

}
