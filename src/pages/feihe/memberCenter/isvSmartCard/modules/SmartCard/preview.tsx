import React from 'react';
import { Slider } from '@alifd/next';
import styles from './index.module.scss';

export default ({ data, dispatch, activeKey, operationKey }) => {
  return (
    <div className={styles.preview}>
      <div
        className={styles['member-benefits-container']}
        style={{
          backgroundImage: `url(${data[activeKey].bgImg})`,
        }}
      >
        {activeKey === 'NotMemberNew' && (
          <div style={{ display: 'flex', alignItems: 'center', padding: '0 5px', marginBottom: '6px' }}>
            <div className={styles.avator} />
            <div style={{ marginLeft: '10px' }}>
              <div style={{ textIndent: '5px' }}>您还不是飞鹤会员哦~</div>
              <img
                className={styles['join-member-button']}
                src="https://img10.360buyimg.com/imgzone/jfs/t1/234660/31/11413/6672/65a87f18F5c3a856d/24f36ff341e30d91.png"
                alt=""
              />
            </div>
          </div>
        )}
        {activeKey !== 'NotMemberNew' && (
          <div
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              justifyContent: 'space-around',
            }}
          >
            <div className={styles.avator} />
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>
                  <div>“昵称”欢迎回到飞鹤之家~</div>
                  <div style={{ display: 'flex', marginTop: '5px', fontSize: '10px ' }}>
                    <div>会员等级：LV0</div>
                    <div style={{ marginLeft: '10px' }}>{activeKey === 'ZROldMember' ? '卓睿星币' : '飞鹤星币'}：0</div>
                  </div>
                </div>
                <img
                  style={{ width: 80, height: 23 }}
                  src="https://img10.360buyimg.com/imgzone/jfs/t1/9981/24/23106/16193/65a87f19F31c00b6e/e96dad3902cf7c71.png"
                  alt=""
                />
              </div>
              <img
                style={{ width: '265px' }}
                src="//img10.360buyimg.com/imgzone/jfs/t1/247571/5/4297/7054/65bb0960Fc25aeea3/d78c1a714da53e36.png"
                alt=""
              />
            </div>
          </div>
        )}

        {/* {data[activeKey].tabs[operationKey].swiperList.length < 4 && ( */}
        {/*  <div */}
        {/*    className={styles['member-benefits-activity']} */}
        {/*    style={{ width: '100%', height: '80px', overflow: 'hidden' }} */}
        {/*  > */}
        {/*    {data[activeKey].tabs[operationKey].swiperList.map((item, index) => ( */}
        {/*      <img */}
        {/*        key={index} */}
        {/*        className={`${styles['member-benefits-activity-link']} `} */}
        {/*        style={{ marginRight: '5px' }} */}
        {/*        src={item?.img} */}
        {/*      /> */}
        {/*    ))} */}
        {/*  </div> */}
        {/* )} */}

        {data[activeKey].tabs[operationKey].swiperList.length > 0 && (
          <div className={styles.sweipebox} style={{ width: '100%', height: '80px', overflow: 'hidden' }}>
            <Slider
              slidesToShow={3}
              arrowPosition="inner"
              dots={false}
              arrows={false}
              lazyLoad
              autoplay={data[activeKey].tabs[operationKey].swiperList.length > 3}
            >
              {data[activeKey].tabs[operationKey].swiperList.map((item, index) => (
                <div key={index}>
                  <img key={index} src={item?.img} />
                </div>
              ))}
            </Slider>
          </div>
        )}
        <div style={{ textAlign: 'center', fontSize: 10, marginTop: activeKey === 'NotMemberNew' ? 6 : 2.5 }}>
          {data[activeKey].tabs[operationKey].bottomTips}
        </div>
      </div>
    </div>
  );
};
