import React, { useEffect, useState } from 'react';
import { Form, DatePicker2, Field, Table, Message, Button } from '@alifd/next';
import constant from '@/utils/constant';
import { dataReportExport, dataReportQuery } from '@/api/v99101';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import LzPagination from '@/components/LzPagination';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [columns, setColumns] = useState<any[]>([]);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const transformColumns = (columnItems) => {
    // 深拷贝源数组以避免修改原数据
    const clonedColumns = JSON.parse(JSON.stringify(columnItems));
    if (clonedColumns.length === 0) return clonedColumns;

    // 处理第一个根节点（添加 lock 和 align）
    const firstNode = clonedColumns[0];
    firstNode.lock = 'left';
    firstNode.align = 'center';
    firstNode.width = '120px';

    // 初始化队列并设置计数器
    const queue = [...clonedColumns];
    let counter = 1;

    // 若第一个节点是叶子节点，设置 dataIndex
    if (!firstNode.children?.length) {
      firstNode.dataIndex = `line${counter++}`;
    }
    // 层序遍历处理其他节点
    while (queue.length > 0) {
      const node = queue.shift();
      // 跳过已处理的首节点
      if (node === firstNode) {
        if (node.children?.length) queue.push(...node.children);
        continue;
      }
      // 为所有非首节点添加 align
      node.align = 'center';
      node.width = '150px';
      // 处理叶子节点的 dataIndex
      if (!node.children?.length) {
        node.dataIndex = `line${counter++}`;
      } else {
        // 非叶子节点则将其子节点入队
        queue.push(...node.children);
      }
    }
    return clonedColumns;
  };
  const transformArray = (arr: any[]): any[] => {
    return arr.map((item) => {
      if (item.children) {
        item.children = transformArray(item.children);
        if (item.children.length === 1 && !item.children[0].children) {
          const { children, ...rest } = item;
          return rest;
        }
      }
      return item;
    });
  };
  const generateColumnsFromLists = (list: any[]): any[] => {
    const [list1, list2, list3] = list;
    // 构建动态分组结构
    let currentSection = null;
    let currentPhase = null;
    const sections = [];

    for (let i = 1; i < list1.length; i++) {
      const sectionTitle = list1[i];
      const phaseTitle = list2[i];
      const itemTitle = list3[i];

      // 处理段变化
      if (!currentSection || currentSection.title !== sectionTitle) {
        currentSection && sections.push(currentSection);
        currentSection = { title: sectionTitle, phases: [] };
        currentPhase = { title: phaseTitle, items: [] };
        currentSection.phases.push(currentPhase);
      }

      // 处理阶段变化
      if (currentPhase.title !== phaseTitle) {
        currentPhase = { title: phaseTitle, items: [] };
        currentSection.phases.push(currentPhase);
      }

      currentPhase.items.push(itemTitle);
    }
    currentSection && sections.push(currentSection); // 添加最后一个段

    // 转换为目标结构
    const _columns = [{ title: list1[0] }];
    sections.forEach((section) => {
      const entry = { title: section.title } as any;
      if (section.phases.length === 1 && section.phases[0].title === section.title) {
        // 直接子项结构
        entry.children = section.phases[0].items.map((item) => ({ title: item }));
      } else {
        // 嵌套阶段结构
        entry.children = section.phases.map((phase) => ({
          title: phase.title,
          children: phase.items.map((item) => ({ title: item })),
        }));
      }
      _columns.push(entry);
    });
    transformArray(_columns);
    return _columns;
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.startDate = dayJs(query.dateRange[0]).format('YYYY-MM-DD HH:mm:ss');
    query.endDate = dayJs(query.dateRange[1]).format('YYYY-MM-DD HH:mm:ss');
    dataReportQuery(query)
      .then((res): void => {
        pageInfo.total = res.total;
        pageInfo.pageSize = res.pageSize;
        pageInfo.pageNum = res.pageNum;
        setPage(pageInfo);
        const _columnsTitle = generateColumnsFromLists(res.title);
        const _tableData = res.content.map((item) => {
          const row = {};
          item.forEach((data, index) => {
            row[`line${index + 1}`] = data;
          });
          return row;
        });
        setTableData(_tableData);
        setColumns(transformColumns(_columnsTitle));
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    formValue.startDate = dayJs(formValue.dateRange[0]).format('YYYY-MM-DD HH:mm:ss');
    formValue.endDate = dayJs(formValue.dateRange[1]).format('YYYY-MM-DD HH:mm:ss');
    dataReportExport(formValue).then((data: any) => downloadExcel(data, '活动数据'));
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="领取时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table.StickyLock columns={columns} dataSource={tableData} loading={loading} style={{ marginTop: 20 }} />
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
