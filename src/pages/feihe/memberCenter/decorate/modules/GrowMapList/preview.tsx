import React from 'react';
import styles from './index.module.scss';
import { Slider } from '@alifd/next';

export default ({ data, dispatch }) => {
  return (
    <div className={styles.preview}>
      <div className={styles['grow-map-container']}>
        {/* <Slider slidesToShow={5} arrowPosition="inner" dots={false} arrows={false} lazyLoad> */}
        <div className={styles['grow-map-list']}>
          {data.growMapList.map((item, index) => {
            return (
              <div key={index} className={styles['grow-map-item']}>
                <div className={styles['grow-map-item-icon']} style={{ backgroundImage: `url(${item.img})` }}></div>
                <span className={styles['grow-map-item-name']}>{item.name}</span>
              </div>
            );
          })}
        </div>

        {/* </Slider> */}
      </div>
    </div>
  );
};
