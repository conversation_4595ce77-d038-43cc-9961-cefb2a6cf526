import ChooseSku from '@/components/ChooseSku';
import LzPanel from '@/components/LzPanel';
import { formItemLayout } from '@/pages/mengniu/public/scripts/commonSetting';
import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import constant from '@/utils/constant';
import { Box, Card, DatePicker2, Form, Input, NumberPicker, Radio } from '@alifd/next';
import React from 'react';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

export default function JoinRule({ data, resetData, updateCurrentModuleData, field, state }) {
  const {
    evaluateStartTime,
    evaluateEndTime,
    evaluateStar,
    evaluateWords,
    evaluatePic,
    evaluateSkuLimit,
    evaluateSkuList,
    evaluatePrice,
  } = data;

  const validateValuateSku = (rule, values, callback) => {
    if (values == 'true') {
      callback('请设置评价商品');
    }
    callback();
  };

  return (
    <Card free className={decorateStyles['card-container']}>
      <Card.Header className={decorateStyles['card-header']} title={<span className="crm-label">参与规则</span>} />
      <Card.Content>
        <LzPanel>
          <Form field={field} {...formItemLayout}>
            <Form.Item label="评价时间" required>
              <DatePicker2.RangePicker
                className="w-300"
                format={dateFormat}
                outputFormat={dateFormat}
                timePanelProps={{ defaultValue: '00:00:00' }}
                hasClear={false}
                showTime
                value={[evaluateStartTime, evaluateEndTime]}
                onChange={(value) => {
                  updateCurrentModuleData({
                    evaluateStartTime: value[0],
                    evaluateEndTime: value[1],
                  });
                }}
              />
            </Form.Item>
            <Form.Item label="评价星级" required>
              <Radio.Group
                value={evaluateStar}
                onChange={(value) => {
                  updateCurrentModuleData({
                    evaluateStar: value,
                  });
                }}
              >
                <Radio value={1}>一星及以上</Radio>
                <Radio value={2}>二星及以上</Radio>
                <Radio value={3}>三星及以上</Radio>
                <Radio value={4}>四星及以上</Radio>
                <Radio value={5}>五星</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="评价字数" required>
              <Radio.Group
                value={!!evaluateWords}
                onChange={(value) => {
                  updateCurrentModuleData({
                    evaluateWords: value === false ? 0 : 15,
                  });
                }}
              >
                <Radio value={false}>不限制</Radio>
                <Radio value={true}>
                  <NumberPicker
                    disabled={evaluateWords === 0}
                    style={{ width: '200px' }}
                    type="inline"
                    min={1}
                    max={999999}
                    step={1}
                    value={evaluateWords}
                    label={'限制'}
                    innerAfter={'个字'}
                    onChange={(value) => {
                      updateCurrentModuleData({
                        evaluateWords: value,
                      });
                    }}
                  />
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="评价图片" required>
              <Radio.Group
                value={!!evaluatePic}
                onChange={(value) => {
                  updateCurrentModuleData({
                    evaluatePic: value === false ? 0 : 1,
                  });
                }}
              >
                <Radio value={false}>不限制</Radio>
                <Radio value={true}>
                  <NumberPicker
                    disabled={evaluatePic === 0}
                    style={{ width: '200px' }}
                    type="inline"
                    min={1}
                    max={9}
                    step={1}
                    value={evaluatePic}
                    label={'限制'}
                    innerAfter={'张图片'}
                    onChange={(value) => {
                      updateCurrentModuleData({
                        evaluatePic: value,
                      });
                    }}
                  />
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name="evaluateSku"
              label="评价商品"
              required
              validator={validateValuateSku}
              extra={<div className="next-form-item-extra">未指定商品时，点击【立即参与】跳转到用户评价中心</div>}
            >
              <Input htmlType="hidden" value={(evaluateSkuLimit == 2 && evaluateSkuList.length === 0).toString()} />
              <Box>
                <Box>
                  <Radio.Group
                    value={evaluateSkuLimit}
                    onChange={(value) => {
                      updateCurrentModuleData({
                        evaluateSkuLimit: value,
                      });
                      setTimeout(() => {
                        field.validate('evaluateSku');
                      }, 20);
                    }}
                  >
                    <Radio value={1}>不限制</Radio>
                    <Radio value={2}>限制</Radio>
                  </Radio.Group>
                </Box>
                {evaluateSkuLimit == 2 && (
                  <Box style={{ marginTop: 15 }}>
                    <ChooseSku
                      value={evaluateSkuList}
                      onChange={(skus) => {
                        updateCurrentModuleData({
                          evaluateSkuList: skus.map((sku) => ({
                            ...sku,
                            skuImg: sku.skuMainPicture,
                            skuPrice: sku.jdPrice,
                          })),
                        });
                        setTimeout(() => {
                          field.validate('evaluateSku');
                        }, 20);
                      }}
                      max={500}
                      showDiscount={false}
                      showSalePrice={false}
                      showSkuMaxNum={false}
                    />
                  </Box>
                )}
              </Box>
            </Form.Item>
            <Form.Item
              name="evaluatePrice"
              label="订单金额满足"
              min={0}
              max={999999}
              required
              requiredMessage="请输入订单金额"
            >
              <NumberPicker
                style={{ width: '200px' }}
                type="inline"
                min={0}
                max={999999}
                step={1}
                value={evaluatePrice}
                innerAfter={'元'}
                onChange={(value) => {
                  updateCurrentModuleData({
                    evaluatePrice: value,
                  });
                }}
              />
            </Form.Item>
          </Form>
        </LzPanel>
      </Card.Content>
    </Card>
  );
}
