/**
 * Author: <PERSON><PERSON><PERSON>e
 * Date: 2023-06-05 15:37
 * Description:
 */
import React, { useReducer } from 'react';
// @ts-ignore
import styles from '../../index.module.scss';
import LzTipPanel from '@/components/LzTipPanel';
import { Field, Form, Radio, NumberPicker, Button, Divider, Message } from '@alifd/next';
// import constant from '@/utils/constant';
// import format from '@/utils/format';
// import dayjs from 'dayjs';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
// const { RangePicker } = DatePicker2;

// const dateFormat = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: any = {
  labelAlign: 'top',
  colon: true,
};
const TIP = (
  <div style={{ color: 'black' }}>
    <div>任务说明</div>
    <div>1.非会员用户成功开卡后可以获得抽奖机会</div>
    <div>2.在任务时间内退会后重新开卡不赠送游戏机会</div>
    <div>3.单家开卡、全部开卡，不支持勾选部分店铺，不支持新增或者删除店铺</div>
  </div>
);
export default ({ onCancel, onSubmit, editValue, formData }) => {
  const defaultValue = {
    // 任务时间限制方式 1 活动期间 2 指定时间
    taskTimeLimitWay: 1,
    // 开卡条件  1 单家开卡 2 全部开卡
    perOperateCount: 1,
    // 赠送抽奖机会次数
    perLotteryCount: 1,
    // 任务时间
    // taskRangeDate: formData.rangeDate,
    // taskStartTime: format.formatDateTimeDayjs(formData.rangeDate[0]),
    // taskEndTime: format.formatDateTimeDayjs(formData.rangeDate[1]),
  };
  const [taskData, setTaskData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const setData = (data) => {
    setTaskData(data);
  };

  const submit = () => {
    if (!taskData.perLotteryCount) {
      Message.error('请输入赠送抽奖机会次数');
      return;
    }
    onSubmit({ ...taskData, taskType: 13 });
  };
  // const onDataRangeChange = (taskRangeDate: any[]) => {
  //   setTaskData({ taskRangeDate });
  //   setData({
  //     taskStartTime: format.formatDateTimeDayjs(taskRangeDate[0]),
  //     taskEndTime: format.formatDateTimeDayjs(taskRangeDate[1]),
  //   });
  // };

  return (
    <div className={styles.lzTask}>
      <LzTipPanel message={TIP} />
      <Form {...formItemLayout} field={field}>
        <FormItem label="任务时间限制">
          <RadioGroup value={taskData.taskTimeLimitWay} onChange={(taskTimeLimitWay) => setData({ taskTimeLimitWay })}>
            <Radio id="1" value={1}>
              活动期间有效
            </Radio>
            <Radio id="2" value={2}>
              不限制
            </Radio>
          </RadioGroup>
          {/* {taskData.taskTimeLimitWay === 2 && ( */}
          {/*  <div className={styles.panel}> */}
          {/*    <RangePicker */}
          {/*      onChange={onDataRangeChange} */}
          {/*      style={{ width: '100%' }} */}
          {/*      name="rangeDate" */}
          {/*      showTime */}
          {/*      hasClear={false} */}
          {/*      format={dateFormat} */}
          {/*      value={taskData.taskRangeDate} */}
          {/*      disabledDate={(date) => { */}
          {/*        return ( */}
          {/*          date.valueOf() < dayjs(formData.rangeDate[0]).subtract(1, 'day').valueOf() || */}
          {/*          date.valueOf() > dayjs(formData.rangeDate[1]).valueOf() */}
          {/*        ); */}
          {/*      }} */}
          {/*    /> */}
          {/*  </div> */}
          {/* )} */}
        </FormItem>
        <FormItem label="开卡条件">
          <RadioGroup value={taskData.perOperateCount} onChange={(perOperateCount) => setData({ perOperateCount })}>
            <Radio id="1" value={1}>
              单家开卡
            </Radio>
            <Radio id="2" value={2}>
              全部开卡
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="任务条件">
          <div className={styles.panel}>
            成功开卡{taskData.perOperateCount === 1 ? '每家店铺' : '全部5家店铺'}，赠送
            <NumberPicker
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
              value={taskData.perLotteryCount}
              onChange={(perLotteryCount) => setData({ perLotteryCount })}
            />
            次抽奖机会
          </div>
        </FormItem>
      </Form>
      <Divider />
      <div className={styles.footer}>
        <Button onClick={onCancel}>取消</Button>
        <Button onClick={submit} type="primary">
          确定
        </Button>
      </div>
    </div>
  );
};
